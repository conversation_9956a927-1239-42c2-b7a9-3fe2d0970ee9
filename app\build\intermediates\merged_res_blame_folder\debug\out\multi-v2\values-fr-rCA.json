{"logs": [{"outputFile": "com.pdfviewer.app-mergeDebugResources-40:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,8205", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,8287"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\763e7e3c7daae90be5c167cae26ed6a3\\transformed\\material-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1151,1248,1328,1390,1482,1549,1623,1684,1763,1827,1881,1997,2056,2118,2172,2254,2383,2475,2559,2703,2782,2863,2956,3011,3062,3128,3207,3288,3379,3451,3529,3604,3676,3773,3850,3948,4046,4124,4205,4305,4362,4428,4511,4598,4660,4724,4787,4889,4996,5093,5202,5261,5316", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,92,54,50,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,58,54,88", "endOffsets": "278,385,493,575,676,773,873,995,1080,1146,1243,1323,1385,1477,1544,1618,1679,1758,1822,1876,1992,2051,2113,2167,2249,2378,2470,2554,2698,2777,2858,2951,3006,3057,3123,3202,3283,3374,3446,3524,3599,3671,3768,3845,3943,4041,4119,4200,4300,4357,4423,4506,4593,4655,4719,4782,4884,4991,5088,5197,5256,5311,5400"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3083,3190,3298,3380,3481,3578,3678,3800,3885,3951,4048,4128,4190,4282,4349,4423,4484,4563,4627,4681,4797,4856,4918,4972,5054,5183,5275,5359,5503,5582,5663,5756,5811,5862,5928,6007,6088,6179,6251,6329,6404,6476,6573,6650,6748,6846,6924,7005,7105,7162,7228,7311,7398,7460,7524,7587,7689,7796,7893,8002,8061,8116", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,92,54,50,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,58,54,88", "endOffsets": "328,3185,3293,3375,3476,3573,3673,3795,3880,3946,4043,4123,4185,4277,4344,4418,4479,4558,4622,4676,4792,4851,4913,4967,5049,5178,5270,5354,5498,5577,5658,5751,5806,5857,5923,6002,6083,6174,6246,6324,6399,6471,6568,6645,6743,6841,6919,7000,7100,7157,7223,7306,7393,7455,7519,7582,7684,7791,7888,7997,8056,8111,8200"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\cda01deaece8a11159b1c59d9b483d75\\transformed\\core-1.9.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8292", "endColumns": "100", "endOffsets": "8388"}}]}]}