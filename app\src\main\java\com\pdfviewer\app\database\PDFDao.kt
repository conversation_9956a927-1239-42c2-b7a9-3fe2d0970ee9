package com.pdfviewer.app.database

import androidx.lifecycle.LiveData
import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface PDFDao {
    
    // PDF Files operations - using Flow for reactive queries
    @Query("SELECT * FROM pdf_files ORDER BY lastOpenedTime DESC, name ASC")
    fun getAllPDFs(): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files ORDER BY lastOpenedTime DESC LIMIT 10")
    fun getRecentPDFs(): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files WHERE isFavorite = 1 ORDER BY name ASC")
    fun getFavoritePDFs(): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files WHERE category = :category ORDER BY name ASC")
    fun getPDFsByCategory(category: String): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchPDFs(query: String): Flow<List<PDFEntity>>
    
    // Simple synchronous operations for now
    @Query("SELECT * FROM pdf_files WHERE path = :path")
    fun getPDFByPath(path: String): PDFEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertPDF(pdf: PDFEntity): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertPDFs(pdfs: List<PDFEntity>): List<Long>
    
    @Update
    fun updatePDF(pdf: PDFEntity): Int
    
    @Query("UPDATE pdf_files SET readingProgress = :progress, lastOpenedTime = :time WHERE path = :path")
    fun updateReadingProgress(path: String, progress: Int, time: Long): Int
    
    @Query("UPDATE pdf_files SET isFavorite = :isFavorite WHERE path = :path")
    fun updateFavoriteStatus(path: String, isFavorite: Boolean): Int
    
    @Query("UPDATE pdf_files SET pageCount = :pageCount WHERE path = :path")
    fun updatePageCount(path: String, pageCount: Int): Int
    
    @Delete
    fun deletePDF(pdf: PDFEntity): Int
    
    @Query("DELETE FROM pdf_files WHERE path = :path")
    fun deletePDFByPath(path: String): Int
    
    @Query("DELETE FROM pdf_files")
    fun deleteAllPDFs(): Int
    
    // Statistics queries
    @Query("SELECT COUNT(*) FROM pdf_files")
    fun getTotalPDFCount(): Int
    
    @Query("SELECT COUNT(*) FROM pdf_files WHERE isFavorite = 1")
    fun getFavoritePDFCount(): Int
    
    @Query("SELECT COUNT(*) FROM pdf_files WHERE readingProgress > 0")
    fun getStartedPDFCount(): Int
    
    @Query("SELECT COUNT(*) FROM pdf_files WHERE readingProgress >= 100")
    fun getCompletedPDFCount(): Int
    
    @Query("SELECT AVG(readingProgress) FROM pdf_files WHERE readingProgress > 0")
    fun getAverageReadingProgress(): Double?
}