<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- Document background -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M6,2 L6,22 L18,22 L18,8 L12,2 Z" />
    
    <!-- Document border -->
    <path
        android:strokeColor="#E0E0E0"
        android:strokeWidth="1"
        android:fillColor="@android:color/transparent"
        android:pathData="M6,2 L6,22 L18,22 L18,8 L12,2 Z" />
    
    <!-- Folded corner -->
    <path
        android:fillColor="#F0F0F0"
        android:pathData="M12,2 L12,8 L18,8 Z" />
    
    <!-- PDF text -->
    <path
        android:fillColor="#FF6200EE"
        android:pathData="M8,12 L8,16 L9.5,16 Q10.5,16 10.5,15 Q10.5,14 9.5,14 L9,14 L9,12 Z M9,13 L9,15 L9.5,15 Q10,15 10,14 Q10,13 9.5,13 Z" />
    
    <path
        android:fillColor="#FF6200EE"
        android:pathData="M11,12 L11,16 L12.5,16 Q13.5,16 13.5,14 Q13.5,12 12.5,12 Z M12,13 L12,15 L12.5,15 Q13,15 13,14 Q13,13 12.5,13 Z" />
    
    <path
        android:fillColor="#FF6200EE"
        android:pathData="M14,12 L14,16 L15,16 L15,14.5 L16,14.5 L16,13.5 L15,13.5 L15,13 L16.5,13 L16.5,12 Z" />
</vector>