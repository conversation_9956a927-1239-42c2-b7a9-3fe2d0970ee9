{"logs": [{"outputFile": "com.pdfviewer.app-mergeDebugResources-40:/values-ta/values-ta.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\cda01deaece8a11159b1c59d9b483d75\\transformed\\core-1.9.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8281", "endColumns": "100", "endOffsets": "8377"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\763e7e3c7daae90be5c167cae26ed6a3\\transformed\\material-1.8.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1102,1210,1278,1339,1447,1514,1600,1658,1742,1809,1863,1986,2048,2111,2165,2253,2381,2467,2549,2681,2761,2842,2931,2988,3040,3106,3191,3279,3371,3440,3517,3597,3665,3764,3847,3939,4033,4107,4193,4287,4337,4403,4488,4575,4638,4703,4766,4874,4977,5075,5180,5241,5297", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,87,85,83,102,93,108,117,83,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,88,56,51,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,60,55,85", "endOffsets": "267,355,441,525,628,722,831,949,1033,1097,1205,1273,1334,1442,1509,1595,1653,1737,1804,1858,1981,2043,2106,2160,2248,2376,2462,2544,2676,2756,2837,2926,2983,3035,3101,3186,3274,3366,3435,3512,3592,3660,3759,3842,3934,4028,4102,4188,4282,4332,4398,4483,4570,4633,4698,4761,4869,4972,5070,5175,5236,5292,5378"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3088,3176,3262,3346,3449,3543,3652,3770,3854,3918,4026,4094,4155,4263,4330,4416,4474,4558,4625,4679,4802,4864,4927,4981,5069,5197,5283,5365,5497,5577,5658,5747,5804,5856,5922,6007,6095,6187,6256,6333,6413,6481,6580,6663,6755,6849,6923,7009,7103,7153,7219,7304,7391,7454,7519,7582,7690,7793,7891,7996,8057,8113", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,87,85,83,102,93,108,117,83,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,88,56,51,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,60,55,85", "endOffsets": "317,3171,3257,3341,3444,3538,3647,3765,3849,3913,4021,4089,4150,4258,4325,4411,4469,4553,4620,4674,4797,4859,4922,4976,5064,5192,5278,5360,5492,5572,5653,5742,5799,5851,5917,6002,6090,6182,6251,6328,6408,6476,6575,6658,6750,6844,6918,7004,7098,7148,7214,7299,7386,7449,7514,7577,7685,7788,7886,7991,8052,8108,8194"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,8199", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,8276"}}]}]}