package com.pdfviewer.app.repository

import android.content.Context
import android.os.Environment
import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import com.pdfviewer.app.database.BookmarkEntity
import com.pdfviewer.app.database.PDFDao
import com.pdfviewer.app.database.PDFEntity
import com.pdfviewer.app.database.ReadingSessionEntity
import com.pdfviewer.app.model.PDFCategory
import com.pdfviewer.app.model.PDFFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.io.File

class PDFRepository(
    private val pdfDao: PDFDao,
    private val context: Context
) {
    
    // PDF Files operations
    fun getAllPDFs(): Flow<List<PDFFile>> {
        return pdfDao.getAllPDFs().map { entities ->
            entities.map { it.toPDFFile() }
        }
    }
    
    fun getRecentPDFs(): Flow<List<PDFFile>> {
        return pdfDao.getRecentPDFs().map { entities ->
            entities.map { it.toPDFFile() }
        }
    }
    
    fun getFavoritePDFs(): Flow<List<PDFFile>> {
        return pdfDao.getFavoritePDFs().map { entities ->
            entities.map { it.toPDFFile() }
        }
    }
    
    fun getPDFsByCategory(category: PDFCategory): Flow<List<PDFFile>> {
        return pdfDao.getPDFsByCategory(category.name).map { entities ->
            entities.map { it.toPDFFile() }
        }
    }
    
    fun searchPDFs(query: String): Flow<List<PDFFile>> {
        return pdfDao.searchPDFs(query).map { entities ->
            entities.map { it.toPDFFile() }
        }
    }
    
    suspend fun getPDFByPath(path: String): PDFFile? {
        return withContext(Dispatchers.IO) {
            pdfDao.getPDFByPath(path)?.toPDFFile()
        }
    }
    
    suspend fun insertPDF(pdfFile: PDFFile) {
        withContext(Dispatchers.IO) {
            pdfDao.insertPDF(PDFEntity.fromPDFFile(pdfFile))
        }
    }
    
    suspend fun insertPDFs(pdfFiles: List<PDFFile>) {
        withContext(Dispatchers.IO) {
            val entities = pdfFiles.map { PDFEntity.fromPDFFile(it) }
            pdfDao.insertPDFs(entities)
        }
    }
    
    suspend fun updatePDF(pdfFile: PDFFile) {
        withContext(Dispatchers.IO) {
            pdfDao.updatePDF(PDFEntity.fromPDFFile(pdfFile))
        }
    }
    
    suspend fun updateReadingProgress(path: String, progress: Int) {
        withContext(Dispatchers.IO) {
            pdfDao.updateReadingProgress(path, progress, System.currentTimeMillis())
        }
    }
    
    suspend fun updateFavoriteStatus(path: String, isFavorite: Boolean) {
        withContext(Dispatchers.IO) {
            pdfDao.updateFavoriteStatus(path, isFavorite)
        }
    }
    
    suspend fun updatePageCount(path: String, pageCount: Int) {
        withContext(Dispatchers.IO) {
            pdfDao.updatePageCount(path, pageCount)
        }
    }
    
    suspend fun deletePDF(pdfFile: PDFFile) {
        withContext(Dispatchers.IO) {
            pdfDao.deletePDF(PDFEntity.fromPDFFile(pdfFile))
        }
    }
    
    suspend fun deletePDFByPath(path: String) {
        withContext(Dispatchers.IO) {
            pdfDao.deletePDFByPath(path)
        }
    }
    
    // File scanning operations
    suspend fun scanForPDFs(): List<PDFFile> {
        return withContext(Dispatchers.IO) {
            val pdfFiles = mutableListOf<PDFFile>()
            
            // Scan common directories
            val directories = listOf(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS),
                File(Environment.getExternalStorageDirectory(), "Books"),
                File(Environment.getExternalStorageDirectory(), "PDF"),
                File(Environment.getExternalStorageDirectory(), "Documents")
            )
            
            directories.forEach { directory ->
                if (directory.exists() && directory.isDirectory) {
                    scanDirectory(directory, pdfFiles)
                }
            }
            
            // Update database with scanned files
            val entities = pdfFiles.map { pdfFile ->
                val existingPDF = pdfDao.getPDFByPath(pdfFile.path)
                if (existingPDF != null) {
                    // Update existing file with new metadata but keep user data
                    PDFEntity.fromPDFFile(
                        pdfFile.copy(
                            readingProgress = existingPDF.readingProgress,
                            lastOpenedTime = existingPDF.lastOpenedTime,
                            isFavorite = existingPDF.isFavorite,
                            pageCount = existingPDF.pageCount,
                            thumbnailPath = existingPDF.thumbnailPath
                        )
                    )
                } else {
                    PDFEntity.fromPDFFile(pdfFile)
                }
            }
            
            pdfDao.insertPDFs(entities)
            pdfFiles
        }
    }
    
    private fun scanDirectory(directory: File, pdfFiles: MutableList<PDFFile>) {
        try {
            directory.listFiles()?.forEach { file ->
                when {
                    file.isDirectory -> {
                        // Recursively scan subdirectories (limit depth to avoid infinite loops)
                        if (file.name != "." && file.name != ".." && !file.name.startsWith(".")) {
                            scanDirectory(file, pdfFiles)
                        }
                    }
                    file.isFile && file.extension.lowercase() == "pdf" -> {
                        val pdfFile = PDFFile(
                            name = file.name,
                            path = file.absolutePath,
                            size = file.length(),
                            lastModified = file.lastModified(),
                            category = PDFCategory.fromFileName(file.name)
                        )
                        pdfFiles.add(pdfFile)
                    }
                }
            }
        } catch (e: SecurityException) {
            // Handle permission issues
        }
    }
    
    // Bookmarks operations
    fun getBookmarksForPDF(pdfPath: String): Flow<List<BookmarkEntity>> {
        return pdfDao.getBookmarksForPDF(pdfPath)
    }
    
    fun getAllBookmarks(): Flow<List<BookmarkEntity>> {
        return pdfDao.getAllBookmarks()
    }
    
    suspend fun insertBookmark(bookmark: BookmarkEntity): Long {
        return withContext(Dispatchers.IO) {
            pdfDao.insertBookmark(bookmark)
        }
    }
    
    suspend fun deleteBookmark(bookmark: BookmarkEntity) {
        withContext(Dispatchers.IO) {
            pdfDao.deleteBookmark(bookmark)
        }
    }
    
    suspend fun deleteBookmarkByPage(pdfPath: String, pageNumber: Int) {
        withContext(Dispatchers.IO) {
            pdfDao.deleteBookmarkByPage(pdfPath, pageNumber)
        }
    }
    
    // Reading sessions operations
    suspend fun insertReadingSession(session: ReadingSessionEntity) {
        withContext(Dispatchers.IO) {
            pdfDao.insertReadingSession(session)
        }
    }
    
    fun getReadingSessionsForPDF(pdfPath: String): Flow<List<ReadingSessionEntity>> {
        return pdfDao.getReadingSessionsForPDF(pdfPath)
    }
    
    suspend fun getTotalReadingTimeForPDF(pdfPath: String): Long {
        return withContext(Dispatchers.IO) {
            pdfDao.getTotalReadingTimeForPDF(pdfPath) ?: 0L
        }
    }
    
    suspend fun getTotalReadingTimeSince(startTime: Long): Long {
        return withContext(Dispatchers.IO) {
            pdfDao.getTotalReadingTimeSince(startTime) ?: 0L
        }
    }
    
    // Statistics operations
    suspend fun getTotalPDFCount(): Int {
        return withContext(Dispatchers.IO) {
            pdfDao.getTotalPDFCount()
        }
    }
    
    suspend fun getFavoritePDFCount(): Int {
        return withContext(Dispatchers.IO) {
            pdfDao.getFavoritePDFCount()
        }
    }
    
    suspend fun getStartedPDFCount(): Int {
        return withContext(Dispatchers.IO) {
            pdfDao.getStartedPDFCount()
        }
    }
    
    suspend fun getCompletedPDFCount(): Int {
        return withContext(Dispatchers.IO) {
            pdfDao.getCompletedPDFCount()
        }
    }
    
    suspend fun getAverageReadingProgress(): Double {
        return withContext(Dispatchers.IO) {
            pdfDao.getAverageReadingProgress() ?: 0.0
        }
    }
}