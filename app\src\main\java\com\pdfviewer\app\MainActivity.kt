package com.pdfviewer.app

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.pdfviewer.app.adapter.PDFAdapter
import com.pdfviewer.app.databinding.ActivityMainBinding
import com.pdfviewer.app.model.PDFFile
import java.io.File

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var pdfAdapter: PDFAdapter
    private val pdfFiles = mutableListOf<PDFFile>()
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            scanForPDFs()
            Toast.makeText(this, "Permission granted! Scanning for PDFs...", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "Storage permission denied. You can still select files manually.", Toast.LENGTH_LONG).show()
        }
    }
    
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            openPDFWithBuiltInViewer(it, "Selected PDF")
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = "PDF Viewer"
        
        setupRecyclerView()
        setupFAB()
        addSampleData()
        checkPermissions()
        
        Toast.makeText(this, "PDF Viewer ready! Tap items to view PDFs or + to select files", Toast.LENGTH_LONG).show()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_main, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_create_test_pdf -> {
                createTestPDF()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun setupRecyclerView() {
        pdfAdapter = PDFAdapter(
            onItemClick = { pdfFile ->
                // Open PDF with built-in viewer
                openPDFWithBuiltInViewer(pdfFile)
            },
            onMoreClick = { pdfFile, view ->
                // Handle more options
                showPDFOptionsMenu(pdfFile, view)
            }
        )
        
        binding.recyclerViewPdfs.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = pdfAdapter
        }
    }
    
    private fun setupFAB() {
        binding.fabAddPdf.setOnClickListener {
            Toast.makeText(this, "Opening file picker...", Toast.LENGTH_SHORT).show()
            try {
                filePickerLauncher.launch("application/pdf")
            } catch (e: Exception) {
                Toast.makeText(this, "Error opening file picker: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun addSampleData() {
        // Add sample data to demonstrate the UI
        pdfFiles.clear()
        
        // Try to create a test PDF first
        createTestPDF()
        
        pdfFiles.add(
            PDFFile(
                name = "Sample Document",
                path = "/storage/emulated/0/Download/sample.pdf",
                size = 1024 * 1024, // 1MB
                lastModified = System.currentTimeMillis()
            )
        )
        
        pdfFiles.add(
            PDFFile(
                name = "User Manual",
                path = "/storage/emulated/0/Documents/manual.pdf",
                size = 2 * 1024 * 1024, // 2MB
                lastModified = System.currentTimeMillis() - 86400000 // 1 day ago
            )
        )
        
        pdfFiles.add(
            PDFFile(
                name = "Tutorial Guide",
                path = "/storage/emulated/0/Download/tutorial.pdf",
                size = 512 * 1024, // 512KB
                lastModified = System.currentTimeMillis() - 172800000 // 2 days ago
            )
        )
        
        pdfAdapter.notifyDataSetChanged()
    }
    
    private fun createTestPDF() {
        try {
            val testPDF = TestPDFCreator.createTestPDF(this)
            if (testPDF != null && testPDF.exists()) {
                // Add to the beginning of the list
                pdfFiles.add(0, 
                    PDFFile(
                        name = "Test PDF (Created by App)",
                        path = testPDF.absolutePath,
                        size = testPDF.length(),
                        lastModified = testPDF.lastModified()
                    )
                )
                pdfAdapter.notifyDataSetChanged()
                Toast.makeText(this, "✅ Created test PDF: ${testPDF.name}", Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error creating test PDF", e)
            Toast.makeText(this, "Could not create test PDF: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun checkPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ - check for MANAGE_EXTERNAL_STORAGE
            if (Environment.isExternalStorageManager()) {
                scanForPDFs()
            } else {
                Toast.makeText(this, "For full functionality, grant storage permission in settings", Toast.LENGTH_LONG).show()
            }
        } else {
            // Android 10 and below
            when {
                ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED -> {
                    scanForPDFs()
                }
                else -> {
                    requestPermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
                }
            }
        }
    }
    
    private fun scanForPDFs() {
        val realPdfFiles = mutableListOf<PDFFile>()
        
        // Scan common directories for PDF files
        val directories = listOf(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS),
            File(Environment.getExternalStorageDirectory(), "Documents"),
            File(Environment.getExternalStorageDirectory(), "Download")
        )
        
        directories.forEach { directory ->
            if (directory?.exists() == true && directory.isDirectory) {
                scanDirectory(directory, realPdfFiles)
            }
        }
        
        // Add real PDFs to the list (after sample data)
        pdfFiles.addAll(realPdfFiles)
        pdfAdapter.notifyDataSetChanged()
        
        val foundCount = realPdfFiles.size
        if (foundCount > 0) {
            Toast.makeText(this, "Found $foundCount PDF files on device", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun scanDirectory(directory: File, pdfList: MutableList<PDFFile>) {
        try {
            directory.listFiles()?.forEach { file ->
                when {
                    file.isDirectory && file.canRead() -> {
                        scanDirectory(file, pdfList)
                    }
                    file.extension.lowercase() == "pdf" && file.canRead() -> {
                        pdfList.add(
                            PDFFile(
                                name = file.nameWithoutExtension,
                                path = file.absolutePath,
                                size = file.length(),
                                lastModified = file.lastModified()
                            )
                        )
                    }
                }
            }
        } catch (e: SecurityException) {
            Log.e("MainActivity", "Security exception scanning directory: ${directory.absolutePath}", e)
        } catch (e: Exception) {
            Log.e("MainActivity", "Error scanning directory: ${directory.absolutePath}", e)
        }
    }
    
    private fun openPDFWithBuiltInViewer(pdfFile: PDFFile) {
        try {
            val intent = Intent(this, PDFViewerActivity::class.java).apply {
                putExtra("PDF_PATH", pdfFile.path)
                putExtra("PDF_NAME", pdfFile.name)
            }
            startActivity(intent)
            Toast.makeText(this, "Opening ${pdfFile.name}...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_SHORT).show()
            // Fallback to external viewer
            openWithExternalApp(pdfFile)
        }
    }
    
    private fun openPDFWithBuiltInViewer(uri: Uri, name: String) {
        try {
            val intent = Intent(this, PDFViewerActivity::class.java).apply {
                putExtra("PDF_URI", uri)
                putExtra("PDF_NAME", name)
            }
            startActivity(intent)
            Toast.makeText(this, "Opening $name...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_SHORT).show()
            // Fallback to external viewer
            openWithExternalApp(uri)
        }
    }
    
    private fun openWithExternalApp(pdfFile: PDFFile) {
        try {
            val file = File(pdfFile.path)
            if (file.exists()) {
                val uri = androidx.core.content.FileProvider.getUriForFile(
                    this,
                    "${packageName}.fileprovider",
                    file
                )
                openWithExternalApp(uri)
            } else {
                Toast.makeText(this, "PDF file not found: ${pdfFile.path}", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Toast.makeText(this, "Error opening with external app: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun openWithExternalApp(uri: Uri) {
        try {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, "application/pdf")
                flags = Intent.FLAG_ACTIVITY_NO_HISTORY or Intent.FLAG_GRANT_READ_URI_PERMISSION
            }
            
            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
            } else {
                Toast.makeText(this, "No external PDF viewer found. Please install a PDF reader.", Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showPDFOptionsMenu(pdfFile: PDFFile, view: View) {
        // TODO: Implement PDF options menu (delete, share, etc.)
        Toast.makeText(this, "Options for ${pdfFile.name}", Toast.LENGTH_SHORT).show()
    }
}