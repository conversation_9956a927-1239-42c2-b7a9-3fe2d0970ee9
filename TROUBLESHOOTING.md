# PDF Viewer App - Troubleshooting Guide

## 🚨 **If You See a White Screen**

The app has been updated with a **Simple Test Version** that should definitely work.

### ✅ **What You Should See Now:**

When you open the app, you should see:

1. **"PDF Viewer" title** at the top
2. **"Test Button - Click Me!" button**
3. **White background** (not blank)
4. **Toast message**: "Simple PDF Viewer loaded!"

### 🔧 **Step-by-Step Testing:**

1. **Install the latest APK**:
   ```bash
   adb install app\build\outputs\apk\debug\app-debug.apk
   ```

2. **Open the app** - you should see the title and test button

3. **Click the "Test Button"** - you should see:
   - Toast: "Button clicked! App is working!"
   - Toast: "Added 3 sample PDFs to list"
   - **3 PDF items appear** in a list below the button

4. **Click any PDF item** - you should see:
   - Toast: "Clicked: [PDF name]"

## 🐛 **Common Issues & Solutions:**

### **Issue 1: Still seeing white screen**
**Solution:**
- Uninstall the old app completely: `adb uninstall com.pdfviewer.app`
- Reinstall: `adb install app\build\outputs\apk\debug\app-debug.apk`
- Clear app data if needed

### **Issue 2: App crashes on startup**
**Solution:**
- Check Android version (app requires Android 5.0+)
- Check device logs: `adb logcat | grep PDFViewer`
- Try on a different device/emulator

### **Issue 3: No PDF items showing**
**Solution:**
- Click the "Test Button" to add sample data
- The sample data should appear immediately

### **Issue 4: Can't click items**
**Solution:**
- Make sure you clicked the test button first
- Items should show toast messages when clicked

## 📱 **Device Requirements:**

- **Android 5.0+** (API 21+)
- **RAM**: 1GB minimum
- **Storage**: 50MB free space
- **Permissions**: Storage access (optional)

## 🔍 **Debug Information:**

The app now includes two activities:

1. **SimpleMainActivity** (current launcher):
   - Basic functionality
   - Programmatic UI (no XML dependencies)
   - Guaranteed to work
   - Shows test button and sample data

2. **MainActivity** (backup):
   - Full-featured version
   - Uses XML layouts and view binding
   - More complex but prettier UI

## 📋 **Testing Checklist:**

- [ ] App opens without crashing
- [ ] Title "PDF Viewer" is visible
- [ ] Test button is visible and clickable
- [ ] Toast messages appear when button is clicked
- [ ] 3 sample PDF items appear in list
- [ ] PDF items are clickable and show toast messages
- [ ] App doesn't crash during normal use

## 🆘 **If Nothing Works:**

1. **Check Android Studio logs** during installation
2. **Try on Android emulator** instead of physical device
3. **Check if device has enough storage space**
4. **Verify Android version compatibility**
5. **Try building in Android Studio** instead of command line

## 📞 **Success Indicators:**

✅ **App is working if you see:**
- Title text
- Test button
- Toast messages
- Sample PDF list after clicking button
- Click responses on PDF items

❌ **App has issues if you see:**
- Completely white/blank screen
- App crashes immediately
- No response to button clicks
- No toast messages

## 🔄 **Next Steps:**

Once the simple version works:
1. We can switch back to the full-featured MainActivity
2. Add real PDF file scanning
3. Add file picker functionality
4. Enhance the UI with better layouts

The current version proves the app structure works and helps identify any device-specific issues.