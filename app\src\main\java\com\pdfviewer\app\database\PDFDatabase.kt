package com.pdfviewer.app.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

@Database(
    entities = [PDFEntity::class],
    version = 1,
    exportSchema = false
)
abstract class PDFDatabase : RoomDatabase() {
    
    abstract fun pdfDao(): PDFDao
    
    companion object {
        @Volatile
        private var INSTANCE: PDFDatabase? = null
        
        fun getDatabase(context: Context): PDFDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    PDFDatabase::class.java,
                    "pdf_database"
                )
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // Database created for the first time
            }
            
            override fun onOpen(db: SupportSQLiteDatabase) {
                super.onOpen(db)
                // Database opened
            }
        }
    }
}