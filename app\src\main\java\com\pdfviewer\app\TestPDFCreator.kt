package com.pdfviewer.app

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.pdf.PdfDocument
import android.os.Environment
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

object TestPDFCreator {
    
    fun createTestPDF(context: Context): File? {
        return try {
            // Create a new document
            val pdfDocument = PdfDocument()
            
            // Create pages
            for (pageNum in 1..3) {
                val pageInfo = PdfDocument.PageInfo.Builder(595, 842, pageNum).create() // A4 size
                val page = pdfDocument.startPage(pageInfo)
                val canvas = page.canvas
                
                // Draw content on the page
                drawPageContent(canvas, pageNum)
                
                pdfDocument.finishPage(page)
            }
            
            // Save the document
            val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            val file = File(downloadsDir, "test_pdf_viewer.pdf")
            
            val fos = FileOutputStream(file)
            pdfDocument.writeTo(fos)
            pdfDocument.close()
            fos.close()
            
            file
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
    
    private fun drawPageContent(canvas: Canvas, pageNum: Int) {
        val paint = Paint().apply {
            color = Color.BLACK
            textSize = 24f
            isAntiAlias = true
        }
        
        val titlePaint = Paint().apply {
            color = Color.BLUE
            textSize = 36f
            isAntiAlias = true
            isFakeBoldText = true
        }
        
        // Draw title
        canvas.drawText("PDF Viewer Test Document", 50f, 100f, titlePaint)
        
        // Draw page number
        canvas.drawText("Page $pageNum", 50f, 150f, paint)
        
        // Draw some content
        val content = when (pageNum) {
            1 -> listOf(
                "Welcome to the PDF Viewer Test!",
                "",
                "This is a test PDF document created by the app.",
                "It demonstrates that the PDF viewer can:",
                "• Display PDF content properly",
                "• Navigate between pages",
                "• Render text and graphics",
                "• Handle multiple pages",
                "",
                "Features tested:",
                "✓ Text rendering",
                "✓ Page navigation",
                "✓ Zoom functionality",
                "✓ File loading"
            )
            2 -> listOf(
                "Page 2 - Technical Details",
                "",
                "PDF Rendering Method:",
                "• Using Android PdfRenderer API",
                "• Converting pages to bitmaps",
                "• Displaying in ImageView with scroll",
                "",
                "Navigation Controls:",
                "• Previous/Next buttons",
                "• Page counter display",
                "• Smooth page transitions",
                "",
                "File Support:",
                "• Local PDF files",
                "• Selected files via picker",
                "• Shared files from other apps"
            )
            else -> listOf(
                "Page 3 - Success!",
                "",
                "If you can see this page, the PDF viewer is working correctly!",
                "",
                "What this proves:",
                "✓ PDF files can be opened",
                "✓ Multiple pages are supported",
                "✓ Text is rendered clearly",
                "✓ Navigation works properly",
                "",
                "Next steps:",
                "• Try opening other PDF files",
                "• Test the share functionality",
                "• Use external PDF viewers",
                "",
                "The PDF Viewer app is fully functional!"
            )
        }
        
        var y = 200f
        content.forEach { line ->
            if (line.isNotEmpty()) {
                canvas.drawText(line, 50f, y, paint)
            }
            y += 30f
        }
        
        // Draw a simple rectangle
        val rectPaint = Paint().apply {
            color = Color.LTGRAY
            style = Paint.Style.STROKE
            strokeWidth = 2f
        }
        canvas.drawRect(50f, y + 20f, 545f, y + 100f, rectPaint)
        
        // Draw footer
        val footerPaint = Paint().apply {
            color = Color.GRAY
            textSize = 16f
            isAntiAlias = true
        }
        canvas.drawText("Generated by PDF Viewer App", 50f, 800f, footerPaint)
    }
}