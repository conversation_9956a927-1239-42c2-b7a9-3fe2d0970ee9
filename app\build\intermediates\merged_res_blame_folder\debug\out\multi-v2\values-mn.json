{"logs": [{"outputFile": "com.pdfviewer.app-mergeDebugResources-40:/values-mn/values-mn.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\cda01deaece8a11159b1c59d9b483d75\\transformed\\core-1.9.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8005", "endColumns": "100", "endOffsets": "8101"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\763e7e3c7daae90be5c167cae26ed6a3\\transformed\\material-1.8.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1039,1137,1218,1277,1370,1432,1495,1553,1624,1686,1740,1861,1918,1979,2033,2104,2237,2321,2404,2537,2619,2697,2787,2841,2892,2958,3029,3107,3193,3268,3346,3426,3509,3597,3676,3766,3859,3933,4003,4094,4148,4215,4299,4384,4446,4510,4573,4677,4783,4880,4985,5043,5098", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,89,53,50,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,57,54,83", "endOffsets": "260,339,416,495,582,667,765,884,969,1034,1132,1213,1272,1365,1427,1490,1548,1619,1681,1735,1856,1913,1974,2028,2099,2232,2316,2399,2532,2614,2692,2782,2836,2887,2953,3024,3102,3188,3263,3341,3421,3504,3592,3671,3761,3854,3928,3998,4089,4143,4210,4294,4379,4441,4505,4568,4672,4778,4875,4980,5038,5093,5177"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,3329,3414,3512,3631,3716,3781,3879,3960,4019,4112,4174,4237,4295,4366,4428,4482,4603,4660,4721,4775,4846,4979,5063,5146,5279,5361,5439,5529,5583,5634,5700,5771,5849,5935,6010,6088,6168,6251,6339,6418,6508,6601,6675,6745,6836,6890,6957,7041,7126,7188,7252,7315,7419,7525,7622,7727,7785,7840", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,89,53,50,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,57,54,83", "endOffsets": "310,3081,3158,3237,3324,3409,3507,3626,3711,3776,3874,3955,4014,4107,4169,4232,4290,4361,4423,4477,4598,4655,4716,4770,4841,4974,5058,5141,5274,5356,5434,5524,5578,5629,5695,5766,5844,5930,6005,6083,6163,6246,6334,6413,6503,6596,6670,6740,6831,6885,6952,7036,7121,7183,7247,7310,7414,7520,7617,7722,7780,7835,7919"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,7924", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,8000"}}]}]}