package com.pdfviewer.app.database

import androidx.lifecycle.LiveData
import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface PDFDao {
    
    // PDF Files operations
    @Query("SELECT * FROM pdf_files ORDER BY lastOpenedTime DESC, name ASC")
    fun getAllPDFs(): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files ORDER BY lastOpenedTime DESC LIMIT 10")
    fun getRecentPDFs(): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files WHERE isFavorite = 1 ORDER BY name ASC")
    fun getFavoritePDFs(): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files WHERE category = :category ORDER BY name ASC")
    fun getPDFsByCategory(category: String): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchPDFs(query: String): Flow<List<PDFEntity>>
    
    @Query("SELECT * FROM pdf_files WHERE path = :path")
    suspend fun getPDFByPath(path: String): PDFEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPDF(pdf: PDFEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPDFs(pdfs: List<PDFEntity>)
    
    @Update
    suspend fun updatePDF(pdf: PDFEntity)
    
    @Query("UPDATE pdf_files SET readingProgress = :progress, lastOpenedTime = :time WHERE path = :path")
    suspend fun updateReadingProgress(path: String, progress: Int, time: Long): Int
    
    @Query("UPDATE pdf_files SET isFavorite = :isFavorite WHERE path = :path")
    suspend fun updateFavoriteStatus(path: String, isFavorite: Boolean): Int
    
    @Query("UPDATE pdf_files SET pageCount = :pageCount WHERE path = :path")
    suspend fun updatePageCount(path: String, pageCount: Int): Int
    
    @Delete
    suspend fun deletePDF(pdf: PDFEntity)
    
    @Query("DELETE FROM pdf_files WHERE path = :path")
    suspend fun deletePDFByPath(path: String): Int
    
    @Query("DELETE FROM pdf_files")
    suspend fun deleteAllPDFs(): Int
    
    // Bookmarks operations
    @Query("SELECT * FROM bookmarks WHERE pdfPath = :pdfPath ORDER BY pageNumber ASC")
    fun getBookmarksForPDF(pdfPath: String): Flow<List<BookmarkEntity>>
    
    @Query("SELECT * FROM bookmarks ORDER BY createdTime DESC")
    fun getAllBookmarks(): Flow<List<BookmarkEntity>>
    
    @Insert
    suspend fun insertBookmark(bookmark: BookmarkEntity): Long
    
    @Delete
    suspend fun deleteBookmark(bookmark: BookmarkEntity)
    
    @Query("DELETE FROM bookmarks WHERE pdfPath = :pdfPath AND pageNumber = :pageNumber")
    suspend fun deleteBookmarkByPage(pdfPath: String, pageNumber: Int): Int
    
    @Query("DELETE FROM bookmarks WHERE pdfPath = :pdfPath")
    suspend fun deleteBookmarksForPDF(pdfPath: String): Int
    
    // Reading sessions operations
    @Insert
    suspend fun insertReadingSession(session: ReadingSessionEntity)
    
    @Query("SELECT * FROM reading_sessions WHERE pdfPath = :pdfPath ORDER BY startTime DESC")
    fun getReadingSessionsForPDF(pdfPath: String): Flow<List<ReadingSessionEntity>>
    
    @Query("SELECT SUM(totalReadingTime) FROM reading_sessions WHERE pdfPath = :pdfPath")
    suspend fun getTotalReadingTimeForPDF(pdfPath: String): Long?
    
    @Query("SELECT SUM(totalReadingTime) FROM reading_sessions WHERE startTime >= :startTime")
    suspend fun getTotalReadingTimeSince(startTime: Long): Long?
    
    // Statistics queries
    @Query("SELECT COUNT(*) FROM pdf_files")
    suspend fun getTotalPDFCount(): Int
    
    @Query("SELECT COUNT(*) FROM pdf_files WHERE isFavorite = 1")
    suspend fun getFavoritePDFCount(): Int
    
    @Query("SELECT COUNT(*) FROM pdf_files WHERE readingProgress > 0")
    suspend fun getStartedPDFCount(): Int
    
    @Query("SELECT COUNT(*) FROM pdf_files WHERE readingProgress = 100")
    suspend fun getCompletedPDFCount(): Int
    
    @Query("SELECT AVG(readingProgress) FROM pdf_files WHERE readingProgress > 0")
    suspend fun getAverageReadingProgress(): Double?
}