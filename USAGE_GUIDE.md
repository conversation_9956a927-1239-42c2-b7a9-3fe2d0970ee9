# PDF Viewer App - Usage Guide

## What You Should See When Opening the App

When you open the PDF Viewer app, you should see:

1. **Title Bar**: "PDF Viewer" at the top
2. **Sample PDF Files**: 3 sample PDF entries for testing:
   - Sample Document (1.0 MB)
   - User Manual (2.0 MB) 
   - Tutorial Guide (512 KB)
3. **Floating Action Button (+)**: Purple button in bottom-right corner
4. **Toast Message**: "Sample PDFs added for testing. Tap + to select real files."

## Features Available

### ✅ **Working Features:**
- **Sample Data Display**: Shows 3 sample PDF entries with proper formatting
- **File Picker**: Tap the + button to select real PDF files from your device
- **External PDF Viewer**: Opens PDFs using your device's default PDF reader
- **Permission Handling**: Requests storage permissions when needed
- **Modern UI**: Material Design with cards, toolbar, and floating action button

### 📱 **How to Use:**

1. **View Sample Data**: 
   - The app shows 3 sample PDF entries immediately
   - Each entry shows file name, size, and date

2. **Select Real PDF Files**:
   - Tap the purple + button (bottom-right)
   - Choose a PDF file from your device
   - The app will open it in your default PDF viewer

3. **Grant Permissions** (if prompted):
   - Allow storage access to scan for PDF files on your device
   - The app will automatically find and display real PDF files

### 🔧 **Technical Details:**

- **Target**: Android 5.0+ (API 21+)
- **Permissions**: Storage access for reading PDF files
- **File Support**: PDF files (.pdf extension)
- **Integration**: Uses system PDF viewers (like Adobe Reader, Google PDF Viewer, etc.)

### 🐛 **Troubleshooting:**

**If you see no content:**
- Make sure you granted storage permissions
- Try tapping the + button to manually select a PDF file
- Check if you have any PDF files on your device

**If PDFs won't open:**
- Install a PDF reader app (Adobe Reader, Google PDF Viewer, etc.)
- Make sure the PDF file isn't corrupted

**If the + button doesn't work:**
- Make sure you have a file manager or the system file picker available
- Try restarting the app

### 📋 **What's Next:**

The app currently shows sample data and allows manual file selection. Future enhancements could include:
- Real PDF file scanning from device storage
- Built-in PDF viewer (requires additional libraries)
- Bookmarks and favorites
- Search functionality
- File management features

## Installation

The APK is located at: `app\build\outputs\apk\debug\app-debug.apk`

Install using: `adb install app\build\outputs\apk\debug\app-debug.apk`