// Generated by view binder compiler. Do not edit!
package com.pdfviewer.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.pdfviewer.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPdfBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton buttonMore;

  @NonNull
  public final ImageView imageViewThumbnail;

  @NonNull
  public final LinearProgressIndicator progressReading;

  @NonNull
  public final TextView textViewFileDate;

  @NonNull
  public final TextView textViewFileName;

  @NonNull
  public final TextView textViewFileSize;

  private ItemPdfBinding(@NonNull MaterialCardView rootView, @NonNull MaterialButton buttonMore,
      @NonNull ImageView imageViewThumbnail, @NonNull LinearProgressIndicator progressReading,
      @NonNull TextView textViewFileDate, @NonNull TextView textViewFileName,
      @NonNull TextView textViewFileSize) {
    this.rootView = rootView;
    this.buttonMore = buttonMore;
    this.imageViewThumbnail = imageViewThumbnail;
    this.progressReading = progressReading;
    this.textViewFileDate = textViewFileDate;
    this.textViewFileName = textViewFileName;
    this.textViewFileSize = textViewFileSize;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPdfBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPdfBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_pdf, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPdfBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonMore;
      MaterialButton buttonMore = ViewBindings.findChildViewById(rootView, id);
      if (buttonMore == null) {
        break missingId;
      }

      id = R.id.imageViewThumbnail;
      ImageView imageViewThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (imageViewThumbnail == null) {
        break missingId;
      }

      id = R.id.progressReading;
      LinearProgressIndicator progressReading = ViewBindings.findChildViewById(rootView, id);
      if (progressReading == null) {
        break missingId;
      }

      id = R.id.textViewFileDate;
      TextView textViewFileDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewFileDate == null) {
        break missingId;
      }

      id = R.id.textViewFileName;
      TextView textViewFileName = ViewBindings.findChildViewById(rootView, id);
      if (textViewFileName == null) {
        break missingId;
      }

      id = R.id.textViewFileSize;
      TextView textViewFileSize = ViewBindings.findChildViewById(rootView, id);
      if (textViewFileSize == null) {
        break missingId;
      }

      return new ItemPdfBinding((MaterialCardView) rootView, buttonMore, imageViewThumbnail,
          progressReading, textViewFileDate, textViewFileName, textViewFileSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
