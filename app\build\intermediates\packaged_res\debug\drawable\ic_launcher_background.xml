<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Gradient background -->
    <path
        android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient
                android:startColor="#FF6200EE"
                android:endColor="#FF3700B3"
                android:type="linear"
                android:angle="45" />
        </aapt:attr>
    </path>
    
    <!-- Subtle pattern -->
    <path
        android:fillColor="#1AFFFFFF"
        android:pathData="M20,20 L88,20 L88,88 L20,88 Z"
        android:strokeWidth="2"
        android:strokeColor="#1AFFFFFF" />
        
    <path
        android:fillColor="#0DFFFFFF"
        android:pathData="M30,30 L78,30 L78,78 L30,78 Z"
        android:strokeWidth="1"
        android:strokeColor="#0DFFFFFF" />
</vector>