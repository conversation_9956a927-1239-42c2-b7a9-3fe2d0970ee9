<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- App Bar Layout with Collapsing Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:fitsSystemWindows="true"
            app:contentScrim="?attr/colorPrimary"
            app:expandedTitleGravity="start|bottom"
            app:expandedTitleMarginBottom="16dp"
            app:expandedTitleMarginStart="16dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed|snap"
            app:title="PDF Library">

            <!-- Header Background -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/gradient_header"
                app:layout_collapseMode="parallax" />

            <!-- Statistics Cards Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="60dp"
                android:orientation="horizontal"
                android:weightSum="3"
                app:layout_collapseMode="parallax">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    app:cardBackgroundColor="?attr/colorSurfaceVariant"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/textTotalPDFs"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="?attr/textAppearanceHeadlineSmall"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:textStyle="bold"
                            tools:text="24" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total PDFs"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="4dp"
                    android:layout_weight="1"
                    app:cardBackgroundColor="?attr/colorSurfaceVariant"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/textFavorites"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="?attr/textAppearanceHeadlineSmall"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:textStyle="bold"
                            tools:text="8" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Favorites"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_weight="1"
                    app:cardBackgroundColor="?attr/colorSurfaceVariant"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/textCompleted"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="?attr/textAppearanceHeadlineSmall"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:textStyle="bold"
                            tools:text="12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Completed"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Toolbar -->
            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:menu="@menu/menu_main"
                app:navigationIcon="@drawable/ic_menu" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Search and Filter Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Search Bar -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/textInputSearch"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Search PDFs..."
                        app:endIconDrawable="@drawable/ic_search"
                        app:endIconMode="custom"
                        app:startIconDrawable="@drawable/ic_search">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextSearch"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Filter Chips -->
                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chipGroupFilters"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        app:chipSpacingHorizontal="8dp"
                        app:singleSelection="false">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chipAll"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:text="All" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chipRecent"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Recent" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chipFavorites"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Favorites" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chipBooks"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Books" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chipDocuments"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Documents" />

                    </com.google.android.material.chip.ChipGroup>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- PDF List Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:orientation="vertical">

                <!-- Section Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textSectionTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="All PDFs"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonViewMode"
                        style="@style/Widget.Material3.Button.IconButton"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:contentDescription="Change view mode"
                        app:icon="@drawable/ic_view_list" />

                </LinearLayout>

                <!-- RecyclerView for PDF list -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewPdfs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_pdf" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layoutEmptyState"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="32dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:alpha="0.6"
                        android:contentDescription="No PDFs found"
                        android:src="@drawable/ic_pdf_document"
                        android:tint="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="No PDFs found"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center"
                        android:text="Tap the + button to add PDFs\nor grant storage permission to scan automatically"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabAddPdf"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="Add PDF"
        android:text="Add PDF"
        app:icon="@drawable/ic_add"
        app:layout_behavior="com.google.android.material.behavior.HideBottomViewOnScrollBehavior" />

    <!-- Loading Progress -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/progressLoading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:indeterminate="true"
        android:visibility="gone"
        app:layout_anchor="@id/appBarLayout"
        app:layout_anchorGravity="bottom" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>