package com.pdfviewer.app

import android.animation.ValueAnimator
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import android.graphics.Bitmap
import android.graphics.pdf.PdfRenderer
import android.os.ParcelFileDescriptor
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.slider.Slider
import com.google.android.material.snackbar.Snackbar
import com.pdfviewer.app.databinding.ActivityPdfViewerBinding
import com.pdfviewer.app.model.PDFFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.regex.Pattern
import kotlin.math.max
import kotlin.math.min

class PDFViewerActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityPdfViewerBinding
    private var pdfFile: PDFFile? = null
    private var pdfUri: Uri? = null
    private var pdfPath: String? = null
    private var pdfName: String? = null
    
    // PDF Renderer
    private var pdfRenderer: PdfRenderer? = null
    private var currentPage: PdfRenderer.Page? = null
    
    // PDF State
    private var currentPageIndex = 0
    private var pageCount = 0
    private var currentZoom = 1.0f
    private var isNightMode = false
    private var isTextSelectionMode = false
    private var isSearchMode = false
    
    // Auto-scroll
    private var isAutoScrolling = false
    private var autoScrollSpeed = 1 // 1x speed
    private var autoScrollHandler = Handler(Looper.getMainLooper())
    private var autoScrollRunnable: Runnable? = null
    
    // Search
    private var searchResults = mutableListOf<SearchResult>()
    private var currentSearchIndex = 0
    private var searchQuery = ""
    
    // Text Selection
    private var selectedText = ""
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPdfViewerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupPDFViewer()
        setupControls()
        loadPDFFromIntent()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        // Search toolbar setup
        binding.toolbarSearch.setNavigationOnClickListener {
            hideSearchMode()
        }
        
        binding.editTextSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val query = s?.toString()?.trim() ?: ""
                if (query.length >= 2) {
                    searchInPDF(query)
                } else {
                    clearSearchResults()
                }
            }
        })
    }
    
    private fun setupPDFViewer() {
        // Setup ImageView for PDF display with zoom and pan capabilities
        binding.imageViewPdf.apply {
            scaleType = android.widget.ImageView.ScaleType.MATRIX
            
            // Enable zoom gestures
            setOnTouchListener { view, event ->
                // Handle touch events for zoom and pan
                false
            }
        }
    }
    
    private fun setupControls() {
        binding.apply {
            // Page navigation
            buttonPrevPage.setOnClickListener { goToPreviousPage() }
            buttonNextPage.setOnClickListener { goToNextPage() }
            
            // Page slider
            sliderPage.addOnSliderTouchListener(object : Slider.OnSliderTouchListener {
                override fun onStartTrackingTouch(slider: Slider) {}
                override fun onStopTrackingTouch(slider: Slider) {
                    val page = slider.value.toInt() - 1
                    goToPage(page)
                }
            })
            
            // Zoom controls
            buttonZoomIn.setOnClickListener { zoomIn() }
            buttonZoomOut.setOnClickListener { zoomOut() }
            
            // Auto-scroll
            buttonAutoScroll.setOnClickListener { toggleAutoScroll() }
            
            // Reading mode
            buttonReadingMode.setOnClickListener { toggleReadingMode() }
            
            // Search results navigation
            buttonPrevResult.setOnClickListener { goToPreviousSearchResult() }
            buttonNextResult.setOnClickListener { goToNextSearchResult() }
            
            // Text selection actions
            buttonCopy.setOnClickListener { copySelectedText() }
            buttonShare.setOnClickListener { shareSelectedText() }
            buttonHighlight.setOnClickListener { highlightSelectedText() }
            
            // Quick actions FAB
            fabQuickActions.setOnClickListener { showQuickActionsMenu() }
        }
    }
    
    private fun loadPDFFromIntent() {
        // Get PDF info from intent
        pdfPath = intent.getStringExtra("PDF_PATH")
        pdfName = intent.getStringExtra("PDF_NAME") ?: "PDF Document"
        pdfUri = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra("PDF_URI", Uri::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra("PDF_URI")
        }
        
        pdfFile = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra("PDF_FILE", PDFFile::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra("PDF_FILE")
        }
        
        supportActionBar?.title = pdfName
        
        loadPDF()
    }
    
    private fun loadPDF() {
        showLoading(true, "Loading PDF...")
        
        lifecycleScope.launch {
            try {
                val fileDescriptor = withContext(Dispatchers.IO) {
                    when {
                        pdfUri != null -> {
                            contentResolver.openFileDescriptor(pdfUri!!, "r")
                        }
                        pdfPath != null -> {
                            val file = File(pdfPath!!)
                            if (file.exists()) {
                                ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY)
                            } else {
                                null
                            }
                        }
                        else -> null
                    }
                }
                
                withContext(Dispatchers.Main) {
                    if (fileDescriptor != null) {
                        try {
                            pdfRenderer = PdfRenderer(fileDescriptor)
                            pageCount = pdfRenderer!!.pageCount
                            
                            if (pageCount > 0) {
                                showPage(0)
                                setupPageSlider()
                                showLoading(false)
                                Toast.makeText(this@PDFViewerActivity, "PDF loaded: $pageCount pages", Toast.LENGTH_SHORT).show()
                            } else {
                                showError("PDF file is empty or corrupted")
                            }
                        } catch (e: Exception) {
                            Log.e("PDFViewer", "Error creating PdfRenderer", e)
                            showError("Cannot open PDF: ${e.message}")
                        }
                    } else {
                        showError("Cannot access PDF file")
                    }
                }
            } catch (e: Exception) {
                Log.e("PDFViewer", "Error loading PDF", e)
                showError("Error loading PDF: ${e.message}")
            }
        }
    }
    
    private fun showPage(index: Int) {
        try {
            currentPage?.close()
            
            if (pdfRenderer == null) {
                showError("PDF not loaded")
                return
            }
            
            currentPage = pdfRenderer!!.openPage(index)
            currentPageIndex = index
            
            // Create bitmap for the page
            val bitmap = Bitmap.createBitmap(
                (currentPage!!.width * currentZoom).toInt(),
                (currentPage!!.height * currentZoom).toInt(),
                Bitmap.Config.ARGB_8888
            )
            
            // Render page to bitmap
            currentPage!!.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
            
            // Display bitmap in ImageView
            binding.imageViewPdf.setImageBitmap(bitmap)
            
            // Update page info
            updatePageInfo()
            updatePageSlider()
            
        } catch (e: Exception) {
            Log.e("PDFViewer", "Error showing page $index", e)
            showError("Error displaying page: ${e.message}")
        }
    }
    
    // Navigation Methods
    private fun goToPreviousPage() {
        if (currentPageIndex > 0) {
            showPage(currentPageIndex - 1)
        }
    }
    
    private fun goToNextPage() {
        if (currentPageIndex < pageCount - 1) {
            showPage(currentPageIndex + 1)
        }
    }
    
    private fun goToPage(page: Int) {
        if (page in 0 until pageCount) {
            showPage(page)
        }
    }
    
    // Zoom Methods
    private fun zoomIn() {
        currentZoom = min(currentZoom * 1.25f, 5.0f)
        showPage(currentPageIndex) // Re-render with new zoom
        updateZoomLevel()
    }
    
    private fun zoomOut() {
        currentZoom = max(currentZoom / 1.25f, 0.5f)
        showPage(currentPageIndex) // Re-render with new zoom
        updateZoomLevel()
    }
    
    private fun updateZoomLevel() {
        binding.textZoomLevel.text = "${(currentZoom * 100).toInt()}%"
    }
    
    // Auto-scroll Methods
    private fun toggleAutoScroll() {
        if (isAutoScrolling) {
            stopAutoScroll()
        } else {
            startAutoScroll()
        }
    }
    
    private fun startAutoScroll() {
        isAutoScrolling = true
        binding.buttonAutoScroll.setIconResource(R.drawable.ic_pause)
        binding.textScrollSpeed.visibility = View.VISIBLE
        binding.textScrollSpeed.text = "${autoScrollSpeed}x"
        
        autoScrollRunnable = object : Runnable {
            override fun run() {
                if (isAutoScrolling) {
                    // Auto-advance to next page after delay
                    if (currentPageIndex < pageCount - 1) {
                        autoScrollHandler.postDelayed({
                            if (isAutoScrolling) {
                                goToNextPage()
                            }
                        }, (3000 / autoScrollSpeed).toLong()) // Adjust timing based on speed
                    } else {
                        stopAutoScroll() // Stop at last page
                    }
                }
            }
        }
        autoScrollHandler.post(autoScrollRunnable!!)
        
        Snackbar.make(binding.root, "Auto-scroll started", Snackbar.LENGTH_SHORT)
            .setAction("Speed") { showAutoScrollSpeedDialog() }
            .show()
    }
    
    private fun stopAutoScroll() {
        isAutoScrolling = false
        binding.buttonAutoScroll.setIconResource(R.drawable.ic_play_arrow)
        binding.textScrollSpeed.visibility = View.GONE
        
        autoScrollRunnable?.let { autoScrollHandler.removeCallbacks(it) }
        
        Snackbar.make(binding.root, "Auto-scroll stopped", Snackbar.LENGTH_SHORT).show()
    }
    
    private fun showAutoScrollSpeedDialog() {
        val speeds = arrayOf("0.5x", "1x", "1.5x", "2x", "3x")
        val currentIndex = when (autoScrollSpeed) {
            1 -> 1
            2 -> 3
            3 -> 4
            else -> if (autoScrollSpeed < 1) 0 else 2
        }
        
        MaterialAlertDialogBuilder(this)
            .setTitle("Auto-scroll Speed")
            .setSingleChoiceItems(speeds, currentIndex) { dialog, which ->
                autoScrollSpeed = when (which) {
                    0 -> 1 // 0.5x (using 1 as minimum)
                    1 -> 1 // 1x
                    2 -> 2 // 1.5x (using 2)
                    3 -> 2 // 2x
                    4 -> 3 // 3x
                    else -> 1
                }
                binding.textScrollSpeed.text = speeds[which]
                dialog.dismiss()
            }
            .show()
    }
    
    // Search Methods
    private fun showSearchMode() {
        isSearchMode = true
        binding.toolbar.visibility = View.GONE
        binding.toolbarSearch.visibility = View.VISIBLE
        binding.editTextSearch.requestFocus()
        
        // Show keyboard
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        imm.showSoftInput(binding.editTextSearch, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
    }
    
    private fun hideSearchMode() {
        isSearchMode = false
        binding.toolbar.visibility = View.VISIBLE
        binding.toolbarSearch.visibility = View.GONE
        binding.cardSearchResults.visibility = View.GONE
        
        clearSearchResults()
        
        // Hide keyboard
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        imm.hideSoftInputFromWindow(binding.editTextSearch.windowToken, 0)
    }
    
    private fun searchInPDF(query: String) {
        if (query == searchQuery) return
        
        searchQuery = query
        binding.progressSearch.visibility = View.VISIBLE
        
        lifecycleScope.launch {
            try {
                val results = withContext(Dispatchers.IO) {
                    performTextSearch(query)
                }
                
                withContext(Dispatchers.Main) {
                    searchResults.clear()
                    searchResults.addAll(results)
                    currentSearchIndex = 0
                    
                    binding.progressSearch.visibility = View.GONE
                    
                    if (results.isNotEmpty()) {
                        binding.cardSearchResults.visibility = View.VISIBLE
                        binding.textSearchResults.text = "${results.size} results found"
                        goToSearchResult(0)
                    } else {
                        binding.textSearchResults.text = "No results found"
                        binding.cardSearchResults.visibility = View.VISIBLE
                    }
                }
            } catch (e: Exception) {
                Log.e("PDFViewer", "Search error", e)
                binding.progressSearch.visibility = View.GONE
            }
        }
    }
    
    private suspend fun performTextSearch(query: String): List<SearchResult> {
        // This is a simplified search implementation
        // In a real app, you'd extract text from PDF pages and search
        val results = mutableListOf<SearchResult>()
        
        // Simulate search results for demo
        val pattern = Pattern.compile(query, Pattern.CASE_INSENSITIVE)
        
        // For demo purposes, create some mock results
        for (page in 0 until pageCount) {
            // In real implementation, extract text from each page and search
            if (query.lowercase() in listOf("the", "and", "of", "to", "a", "in", "is", "it", "you", "that")) {
                results.add(SearchResult(page, query, "...context with $query..."))
            }
        }
        
        return results
    }
    
    private fun goToPreviousSearchResult() {
        if (searchResults.isNotEmpty()) {
            currentSearchIndex = if (currentSearchIndex > 0) currentSearchIndex - 1 else searchResults.size - 1
            goToSearchResult(currentSearchIndex)
        }
    }
    
    private fun goToNextSearchResult() {
        if (searchResults.isNotEmpty()) {
            currentSearchIndex = if (currentSearchIndex < searchResults.size - 1) currentSearchIndex + 1 else 0
            goToSearchResult(currentSearchIndex)
        }
    }
    
    private fun goToSearchResult(index: Int) {
        if (index in searchResults.indices) {
            val result = searchResults[index]
            showPage(result.pageNumber)
            binding.textSearchResults.text = "Result ${index + 1} of ${searchResults.size}"
        }
    }
    
    private fun clearSearchResults() {
        searchResults.clear()
        currentSearchIndex = 0
        searchQuery = ""
        binding.cardSearchResults.visibility = View.GONE
    }
    
    // Text Selection Methods
    private fun toggleTextSelectionMode() {
        isTextSelectionMode = !isTextSelectionMode
        
        if (isTextSelectionMode) {
            Snackbar.make(binding.root, "Text selection mode enabled", Snackbar.LENGTH_SHORT).show()
            // Enable text selection in PDF viewer
        } else {
            hideTextSelectionToolbar()
            Snackbar.make(binding.root, "Text selection mode disabled", Snackbar.LENGTH_SHORT).show()
        }
    }
    
    private fun showTextSelectionToolbar(text: String) {
        selectedText = text
        binding.cardTextSelection.visibility = View.VISIBLE
        
        // Animate the toolbar appearance
        binding.cardTextSelection.alpha = 0f
        binding.cardTextSelection.animate()
            .alpha(1f)
            .setDuration(200)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()
    }
    
    private fun hideTextSelectionToolbar() {
        binding.cardTextSelection.visibility = View.GONE
        selectedText = ""
    }
    
    private fun copySelectedText() {
        if (selectedText.isNotEmpty()) {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("PDF Text", selectedText)
            clipboard.setPrimaryClip(clip)
            
            Snackbar.make(binding.root, "Text copied to clipboard", Snackbar.LENGTH_SHORT).show()
            hideTextSelectionToolbar()
        }
    }
    
    private fun shareSelectedText() {
        if (selectedText.isNotEmpty()) {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, selectedText)
                putExtra(Intent.EXTRA_SUBJECT, "Text from $pdfName")
            }
            startActivity(Intent.createChooser(shareIntent, "Share Text"))
            hideTextSelectionToolbar()
        }
    }
    
    private fun highlightSelectedText() {
        if (selectedText.isNotEmpty()) {
            // TODO: Implement text highlighting
            Snackbar.make(binding.root, "Text highlighted", Snackbar.LENGTH_SHORT).show()
            hideTextSelectionToolbar()
        }
    }
    
    // Reading Mode
    private fun toggleReadingMode() {
        val isCurrentlyInReadingMode = binding.layoutBottomControls.visibility == View.GONE
        
        if (isCurrentlyInReadingMode) {
            // Exit reading mode
            showSystemUI()
            binding.appBarLayout.visibility = View.VISIBLE
            binding.layoutBottomControls.visibility = View.VISIBLE
            binding.fabQuickActions.show()
            binding.buttonReadingMode.setIconResource(R.drawable.ic_visibility)
        } else {
            // Enter reading mode
            hideSystemUI()
            binding.appBarLayout.visibility = View.GONE
            binding.layoutBottomControls.visibility = View.GONE
            binding.fabQuickActions.hide()
            binding.buttonReadingMode.setIconResource(R.drawable.ic_visibility_off)
        }
    }
    
    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
    }
    
    private fun showSystemUI() {
        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
    }
    
    // Night Mode
    private fun toggleNightMode() {
        isNightMode = !isNightMode
        
        if (isNightMode) {
            // Apply night mode filter
            binding.imageViewPdf.setBackgroundColor(Color.parseColor("#1E1E1E"))
            // Apply color filter to invert colors
            val colorMatrix = android.graphics.ColorMatrix()
            colorMatrix.set(floatArrayOf(
                -1f, 0f, 0f, 0f, 255f,
                0f, -1f, 0f, 0f, 255f,
                0f, 0f, -1f, 0f, 255f,
                0f, 0f, 0f, 1f, 0f
            ))
            binding.imageViewPdf.colorFilter = android.graphics.ColorMatrixColorFilter(colorMatrix)
            Snackbar.make(binding.root, "Night mode enabled", Snackbar.LENGTH_SHORT).show()
        } else {
            // Remove night mode filter
            binding.imageViewPdf.setBackgroundColor(Color.WHITE)
            binding.imageViewPdf.colorFilter = null
            Snackbar.make(binding.root, "Night mode disabled", Snackbar.LENGTH_SHORT).show()
        }
    }
    
    // UI Update Methods
    private fun updatePageInfo() {
        binding.textViewPageInfo.text = "Page ${currentPageIndex + 1} of $pageCount"
        
        // Update navigation buttons
        binding.buttonPrevPage.isEnabled = currentPageIndex > 0
        binding.buttonNextPage.isEnabled = currentPageIndex < pageCount - 1
    }
    
    private fun setupPageSlider() {
        binding.sliderPage.apply {
            valueFrom = 1f
            valueTo = pageCount.toFloat()
            value = 1f
        }
    }
    
    private fun updatePageSlider() {
        binding.sliderPage.value = (currentPageIndex + 1).toFloat()
    }
    
    private fun showLoading(show: Boolean, message: String = "Loading...") {
        binding.layoutLoading.visibility = if (show) View.VISIBLE else View.GONE
        binding.textLoadingStatus.text = message
    }
    
    private fun showError(message: String) {
        showLoading(false)
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.e("PDFViewer", message)
    }
    
    // Quick Actions Menu
    private fun showQuickActionsMenu() {
        val actions = arrayOf(
            "Bookmark Page",
            "Go to Page",
            "Table of Contents",
            "Document Info",
            "Print"
        )
        
        MaterialAlertDialogBuilder(this)
            .setTitle("Quick Actions")
            .setItems(actions) { _, which ->
                when (which) {
                    0 -> bookmarkCurrentPage()
                    1 -> showGoToPageDialog()
                    2 -> showTableOfContents()
                    3 -> showDocumentInfo()
                    4 -> printDocument()
                }
            }
            .show()
    }
    
    private fun bookmarkCurrentPage() {
        // TODO: Implement bookmark functionality
        Snackbar.make(binding.root, "Page ${currentPageIndex + 1} bookmarked", Snackbar.LENGTH_SHORT).show()
    }
    
    private fun showGoToPageDialog() {
        val input = android.widget.EditText(this)
        input.inputType = android.text.InputType.TYPE_CLASS_NUMBER
        input.hint = "Page number (1-$pageCount)"
        
        MaterialAlertDialogBuilder(this)
            .setTitle("Go to Page")
            .setView(input)
            .setPositiveButton("Go") { _, _ ->
                val pageStr = input.text.toString()
                try {
                    val page = pageStr.toInt() - 1
                    if (page in 0 until pageCount) {
                        goToPage(page)
                    } else {
                        Toast.makeText(this, "Invalid page number", Toast.LENGTH_SHORT).show()
                    }
                } catch (e: NumberFormatException) {
                    Toast.makeText(this, "Please enter a valid number", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun showTableOfContents() {
        // TODO: Implement table of contents
        Toast.makeText(this, "Table of contents not available", Toast.LENGTH_SHORT).show()
    }
    
    private fun showDocumentInfo() {
        val info = buildString {
            append("Document: $pdfName\n")
            append("Pages: $pageCount\n")
            append("Current Page: ${currentPageIndex + 1}\n")
            append("Zoom: ${(currentZoom * 100).toInt()}%\n")
            pdfFile?.let { file ->
                append("Size: ${file.getFormattedSize()}\n")
                append("Modified: ${file.getFormattedDate()}\n")
            }
        }
        
        MaterialAlertDialogBuilder(this)
            .setTitle("Document Information")
            .setMessage(info)
            .setPositiveButton("OK", null)
            .show()
    }
    
    private fun printDocument() {
        // TODO: Implement print functionality
        Toast.makeText(this, "Print functionality not implemented", Toast.LENGTH_SHORT).show()
    }
    
    // Menu Handling
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_pdf_viewer, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_search -> {
                showSearchMode()
                true
            }
            R.id.action_bookmark -> {
                bookmarkCurrentPage()
                true
            }
            R.id.action_share -> {
                sharePDF()
                true
            }
            R.id.action_open_external -> {
                openWithExternalApp()
                true
            }
            R.id.action_night_mode -> {
                toggleNightMode()
                true
            }
            R.id.action_text_selection -> {
                toggleTextSelectionMode()
                true
            }
            R.id.action_settings -> {
                showSettings()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun sharePDF() {
        try {
            val uri = pdfUri ?: pdfPath?.let { 
                FileProvider.getUriForFile(this, "${packageName}.fileprovider", File(it))
            }
            
            uri?.let {
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "application/pdf"
                    putExtra(Intent.EXTRA_STREAM, it)
                    putExtra(Intent.EXTRA_SUBJECT, pdfName)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                startActivity(Intent.createChooser(shareIntent, "Share PDF"))
            } ?: run {
                Toast.makeText(this, "Cannot share PDF", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Toast.makeText(this, "Error sharing PDF: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun openWithExternalApp() {
        try {
            val uri = pdfUri ?: pdfPath?.let { 
                FileProvider.getUriForFile(this, "${packageName}.fileprovider", File(it))
            }
            
            uri?.let {
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    setDataAndType(it, "application/pdf")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                
                if (intent.resolveActivity(packageManager) != null) {
                    startActivity(intent)
                } else {
                    Toast.makeText(this, "No external PDF viewer found", Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                Toast.makeText(this, "Cannot open PDF externally", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showSettings() {
        // TODO: Implement settings screen
        Toast.makeText(this, "Settings not implemented yet", Toast.LENGTH_SHORT).show()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopAutoScroll()
        try {
            currentPage?.close()
            pdfRenderer?.close()
        } catch (e: Exception) {
            Log.e("PDFViewer", "Error closing PDF resources", e)
        }
    }
    
    override fun onBackPressed() {
        when {
            isSearchMode -> hideSearchMode()
            binding.layoutBottomControls.visibility == View.GONE -> toggleReadingMode()
            else -> super.onBackPressed()
        }
    }
}

// Data classes for search functionality
data class SearchResult(
    val pageNumber: Int,
    val searchTerm: String,
    val context: String
)