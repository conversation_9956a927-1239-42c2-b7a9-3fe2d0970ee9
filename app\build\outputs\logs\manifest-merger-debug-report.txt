-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:59:9-67:20
	android:grantUriPermissions
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:63:13-47
	android:authorities
		INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:61:13-64
	android:exported
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:62:13-37
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:60:13-62
manifest
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:2:1-70:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\c3e0850c68046896e81e6bc90bcdb463\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.8.0] C:\Gradle\gradle-8.4\caches\transforms-3\763e7e3c7daae90be5c167cae26ed6a3\transformed\material-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.4\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\05c7963c5b78615e2170043d4dd5d7e5\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\2303021cb7f3f78c4bc1b8231e5ae854\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\d81085bc28e6be88deff7313e71896b1\transformed\glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\e854dc173145741c17288d2b8e5ed608\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Gradle\gradle-8.4\caches\transforms-3\0814a142ad1dd5b790d941dd8dce2a92\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\9a6f723b391cea783b6d5ba031a280f0\transformed\activity-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\86718758502cda3d4774f84febd541f1\transformed\activity-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\fe7d5922fdbcbc9c6cb2470ce49bf904\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\3a86e637cb64c92d9bbad09ad42a7764\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\24a33f6c58ce47136219c8ef40630553\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\07475b137d4028d88a8df7bcc2982f6d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\c432a646e9439199a33db5f00cad024b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\078ab102ad2f0d28c3fa3e33c18819c6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\0f4efd14d87c979a750a0e7d631c7215\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\06840955b76f998776603c8e6e239b16\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\a030c6c5ad621cd52b16a01854f99cf6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\545430bea67a79add7af4d74c56bfe4c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\4d678b39a8b578da23bd175cf60b9feb\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\02757f10a323eb196afedb5bf2e46ef8\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\773c56230032821b5a829b1fd0a22389\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\14fb7321df2c8efd84e881f44ffe8981\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\d2dc903d36dff689c28920e041dbe2b4\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\3131f2a29cd97810f7003d6130d3fec8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\20a3fb5df4904e943d3194ad8a03f3b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\ad05cc9213b4d5a7c1c99e670e93e3f9\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\741a16d27febc46cfd6b09efd0f8ad1c\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\fbe264fb89fd1f4b3a60c24cb9f41806\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\2c0589af7253d717d6f1165ec3426855\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\191dc1626970eaebcdb219598a30f7ca\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\1defe1d0ee7d0973992842f9f8efe386\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\35db1c888a22fb05dbfcc41742a79157\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\1901f6135e5fe17d707deb06d80574fa\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\5da1c4ec72ae4d041bbb1baebb9f20c5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\5938433006c76a70637123477baffd0f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\a54ca0debb323b1fa22f46ec4655cbbc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\292e4b7aae0504504f2587c255be9dbe\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\5bfb2059f6551ec4cb012a5f60528c80\transformed\gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Gradle\gradle-8.4\caches\transforms-3\3bc5dbc26f66b2cf1f7ed5940b65c833\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\71ab9a8073c0ca5b314a5a684d37123f\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\2a2991bea4032cd791867b86327229dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\0be700c9754429f0e9d143616dc00db2\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\b0fa39cdab60346c5d4f75d6a2b74611\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\9632c531ef9e8b9b93d38c3393006279\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\041f4add30e80fb020c80f1480edcdce\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\872b7857e8caa0e6434b6ddf3a15bc71\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:5:5-80
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:5:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:7:5-8:40
	tools:ignore
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:8:9-37
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:7:22-79
uses-permission#android.permission.INTERNET
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:9:5-67
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:9:22-64
application
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:11:5-68:19
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:11:5-68:19
MERGED from [com.google.android.material:material:1.8.0] C:\Gradle\gradle-8.4\caches\transforms-3\763e7e3c7daae90be5c167cae26ed6a3\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.8.0] C:\Gradle\gradle-8.4\caches\transforms-3\763e7e3c7daae90be5c167cae26ed6a3\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.4\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.4\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\d81085bc28e6be88deff7313e71896b1\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\d81085bc28e6be88deff7313e71896b1\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\5bfb2059f6551ec4cb012a5f60528c80\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\5bfb2059f6551ec4cb012a5f60528c80\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:20:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:18:9-35
	android:label
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:16:9-41
	android:fullBackupContent
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:14:9-54
	android:roundIcon
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:17:9-54
	tools:targetApi
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:21:9-29
	android:icon
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:15:9-43
	android:allowBackup
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:19:9-47
	android:dataExtractionRules
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:13:9-65
activity#com.pdfviewer.app.MainActivity
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:24:9-40:20
	android:exported
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:26:13-36
	android:theme
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:27:13-63
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:25:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:30:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:mimeType:application/pdf
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:34:13-39:29
action#android.intent.action.VIEW
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:35:17-69
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:35:25-66
category#android.intent.category.DEFAULT
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:36:17-76
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:36:27-73
category#android.intent.category.BROWSABLE
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:37:17-78
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:37:27-75
data
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:38:17-60
	android:mimeType
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:38:23-57
activity#com.pdfviewer.app.PDFViewerActivity
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:43:9-51:20
	android:parentActivityName
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:47:13-55
	android:exported
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:45:13-37
	android:theme
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:46:13-63
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:44:13-46
meta-data#android.support.PARENT_ACTIVITY
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:48:13-50:49
	android:value
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:50:17-46
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:49:17-63
activity#com.pdfviewer.app.SimpleMainActivity
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:54:9-57:66
	android:exported
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:56:13-37
	android:theme
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:57:13-63
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:55:13-47
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:64:13-66:54
	android:resource
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:66:17-51
	android:name
		ADDED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml:65:17-67
uses-sdk
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\c3e0850c68046896e81e6bc90bcdb463\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\c3e0850c68046896e81e6bc90bcdb463\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.8.0] C:\Gradle\gradle-8.4\caches\transforms-3\763e7e3c7daae90be5c167cae26ed6a3\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\Gradle\gradle-8.4\caches\transforms-3\763e7e3c7daae90be5c167cae26ed6a3\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.4\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.4\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\05c7963c5b78615e2170043d4dd5d7e5\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\05c7963c5b78615e2170043d4dd5d7e5\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\2303021cb7f3f78c4bc1b8231e5ae854\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\2303021cb7f3f78c4bc1b8231e5ae854\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\d81085bc28e6be88deff7313e71896b1\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\d81085bc28e6be88deff7313e71896b1\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\e854dc173145741c17288d2b8e5ed608\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\e854dc173145741c17288d2b8e5ed608\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Gradle\gradle-8.4\caches\transforms-3\0814a142ad1dd5b790d941dd8dce2a92\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Gradle\gradle-8.4\caches\transforms-3\0814a142ad1dd5b790d941dd8dce2a92\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\9a6f723b391cea783b6d5ba031a280f0\transformed\activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\9a6f723b391cea783b6d5ba031a280f0\transformed\activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\86718758502cda3d4774f84febd541f1\transformed\activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Gradle\gradle-8.4\caches\transforms-3\86718758502cda3d4774f84febd541f1\transformed\activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\fe7d5922fdbcbc9c6cb2470ce49bf904\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\fe7d5922fdbcbc9c6cb2470ce49bf904\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\3a86e637cb64c92d9bbad09ad42a7764\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\3a86e637cb64c92d9bbad09ad42a7764\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\24a33f6c58ce47136219c8ef40630553\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\24a33f6c58ce47136219c8ef40630553\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\07475b137d4028d88a8df7bcc2982f6d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\07475b137d4028d88a8df7bcc2982f6d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\c432a646e9439199a33db5f00cad024b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\c432a646e9439199a33db5f00cad024b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\078ab102ad2f0d28c3fa3e33c18819c6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\078ab102ad2f0d28c3fa3e33c18819c6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\0f4efd14d87c979a750a0e7d631c7215\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\0f4efd14d87c979a750a0e7d631c7215\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\06840955b76f998776603c8e6e239b16\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\06840955b76f998776603c8e6e239b16\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\a030c6c5ad621cd52b16a01854f99cf6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\a030c6c5ad621cd52b16a01854f99cf6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\545430bea67a79add7af4d74c56bfe4c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\545430bea67a79add7af4d74c56bfe4c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\4d678b39a8b578da23bd175cf60b9feb\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\4d678b39a8b578da23bd175cf60b9feb\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\02757f10a323eb196afedb5bf2e46ef8\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\02757f10a323eb196afedb5bf2e46ef8\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\773c56230032821b5a829b1fd0a22389\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Gradle\gradle-8.4\caches\transforms-3\773c56230032821b5a829b1fd0a22389\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\14fb7321df2c8efd84e881f44ffe8981\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\14fb7321df2c8efd84e881f44ffe8981\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\d2dc903d36dff689c28920e041dbe2b4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\d2dc903d36dff689c28920e041dbe2b4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\3131f2a29cd97810f7003d6130d3fec8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\3131f2a29cd97810f7003d6130d3fec8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\20a3fb5df4904e943d3194ad8a03f3b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\20a3fb5df4904e943d3194ad8a03f3b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\ad05cc9213b4d5a7c1c99e670e93e3f9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\ad05cc9213b4d5a7c1c99e670e93e3f9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\741a16d27febc46cfd6b09efd0f8ad1c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Gradle\gradle-8.4\caches\transforms-3\741a16d27febc46cfd6b09efd0f8ad1c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\fbe264fb89fd1f4b3a60c24cb9f41806\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\fbe264fb89fd1f4b3a60c24cb9f41806\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\2c0589af7253d717d6f1165ec3426855\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\2c0589af7253d717d6f1165ec3426855\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\191dc1626970eaebcdb219598a30f7ca\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\191dc1626970eaebcdb219598a30f7ca\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\1defe1d0ee7d0973992842f9f8efe386\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\1defe1d0ee7d0973992842f9f8efe386\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\35db1c888a22fb05dbfcc41742a79157\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\35db1c888a22fb05dbfcc41742a79157\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\1901f6135e5fe17d707deb06d80574fa\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\1901f6135e5fe17d707deb06d80574fa\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\5da1c4ec72ae4d041bbb1baebb9f20c5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\5da1c4ec72ae4d041bbb1baebb9f20c5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\5938433006c76a70637123477baffd0f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\5938433006c76a70637123477baffd0f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\a54ca0debb323b1fa22f46ec4655cbbc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\a54ca0debb323b1fa22f46ec4655cbbc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\292e4b7aae0504504f2587c255be9dbe\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\292e4b7aae0504504f2587c255be9dbe\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\5bfb2059f6551ec4cb012a5f60528c80\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Gradle\gradle-8.4\caches\transforms-3\5bfb2059f6551ec4cb012a5f60528c80\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Gradle\gradle-8.4\caches\transforms-3\3bc5dbc26f66b2cf1f7ed5940b65c833\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Gradle\gradle-8.4\caches\transforms-3\3bc5dbc26f66b2cf1f7ed5940b65c833\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\71ab9a8073c0ca5b314a5a684d37123f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\71ab9a8073c0ca5b314a5a684d37123f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\2a2991bea4032cd791867b86327229dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\2a2991bea4032cd791867b86327229dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\0be700c9754429f0e9d143616dc00db2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\0be700c9754429f0e9d143616dc00db2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\b0fa39cdab60346c5d4f75d6a2b74611\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\b0fa39cdab60346c5d4f75d6a2b74611\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\9632c531ef9e8b9b93d38c3393006279\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\9632c531ef9e8b9b93d38c3393006279\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\041f4add30e80fb020c80f1480edcdce\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Gradle\gradle-8.4\caches\transforms-3\041f4add30e80fb020c80f1480edcdce\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\872b7857e8caa0e6434b6ddf3a15bc71\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\872b7857e8caa0e6434b6ddf3a15bc71\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\apk apps\pdf\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.4\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.pdfviewer.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.pdfviewer.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
