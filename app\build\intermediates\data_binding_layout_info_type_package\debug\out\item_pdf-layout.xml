<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_pdf" modulePackage="com.pdfviewer.app" filePath="app\src\main\res\layout\item_pdf.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_pdf_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="121" endOffset="51"/></Target><Target id="@+id/imageViewThumbnail" view="ImageView"><Expressions/><location startLine="29" startOffset="12" endLine="36" endOffset="62"/></Target><Target id="@+id/textViewFileName" view="TextView"><Expressions/><location startLine="49" startOffset="12" endLine="58" endOffset="72"/></Target><Target id="@+id/textViewFileSize" view="TextView"><Expressions/><location startLine="66" startOffset="16" endLine="72" endOffset="41"/></Target><Target id="@+id/textViewFileDate" view="TextView"><Expressions/><location startLine="82" startOffset="16" endLine="89" endOffset="47"/></Target><Target id="@+id/progressReading" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="94" startOffset="12" endLine="104" endOffset="44"/></Target><Target id="@+id/buttonMore" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="109" startOffset="8" endLine="117" endOffset="56"/></Target></Targets></Layout>