<?xml version="1.0" encoding="UTF-8"?>
<svg width="192" height="192" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
    <!-- Background circle with gradient -->
    <defs>
        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6200EE;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#3700B3;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
        </filter>
    </defs>
    
    <!-- Main background circle -->
    <circle cx="96" cy="96" r="88" fill="url(#bgGradient)" filter="url(#shadow)"/>
    
    <!-- Document background -->
    <rect x="48" y="40" width="80" height="104" rx="4" ry="4" fill="white" stroke="#E0E0E0" stroke-width="1"/>
    
    <!-- Folded corner -->
    <polygon points="112,40 128,56 128,40" fill="#F0F0F0" stroke="#E0E0E0" stroke-width="1"/>
    <line x1="112" y1="40" x2="128" y2="56" stroke="#E0E0E0" stroke-width="1"/>
    
    <!-- PDF Text -->
    <text x="68" y="80" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#6200EE">PDF</text>
    
    <!-- Document lines -->
    <line x1="56" y1="96" x2="120" y2="96" stroke="#E8E8E8" stroke-width="2"/>
    <line x1="56" y1="104" x2="110" y2="104" stroke="#E8E8E8" stroke-width="2"/>
    <line x1="56" y1="112" x2="115" y2="112" stroke="#E8E8E8" stroke-width="2"/>
    <line x1="56" y1="120" x2="105" y2="120" stroke="#E8E8E8" stroke-width="2"/>
    <line x1="56" y1="128" x2="118" y2="128" stroke="#E8E8E8" stroke-width="2"/>
    
    <!-- Small PDF icon in corner -->
    <circle cx="110" cy="130" r="8" fill="#FF4444"/>
    <text x="110" y="134" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="white" text-anchor="middle">P</text>
</svg>