# PDF Viewer Android App

A comprehensive PDF viewer application for Android with advanced features.

## Features

### Core Features
- **PDF Viewing**: High-quality PDF rendering with smooth scrolling
- **File Browser**: Automatically scans and lists all PDF files on device
- **Recent Files**: Quick access to recently opened PDFs
- **File Picker**: Select PDF files from any location

### Viewing Features
- **Zoom Controls**: Pinch to zoom, zoom in/out buttons
- **Page Navigation**: Swipe between pages, page indicator
- **Night Mode**: Dark theme for comfortable reading
- **Full Screen**: Immersive reading experience

### Organization Features
- **Bookmarks**: Save favorite pages for quick access
- **Search**: Find text within PDF documents (coming soon)
- **File Information**: View file size, modification date, and path

### Sharing & Export
- **Share PDFs**: Share files with other apps
- **File Provider**: Secure file sharing implementation

## Technical Features

### Architecture
- **MVVM Pattern**: Clean architecture with ViewModels
- **Room Database**: Local storage for bookmarks and recent files
- **LiveData**: Reactive UI updates
- **Kotlin Coroutines**: Asynchronous operations

### Libraries Used
- **PDF Viewer**: `com.github.barteksc:android-pdf-viewer`
- **Room Database**: Local data persistence
- **Material Design**: Modern UI components
- **Dexter**: Runtime permission handling
- **RecyclerView**: Efficient list display

### Permissions
- `READ_EXTERNAL_STORAGE`: Access PDF files
- `WRITE_EXTERNAL_STORAGE`: File operations
- `MANAGE_EXTERNAL_STORAGE`: Android 11+ file access

## Installation

1. Clone the repository
2. Open in Android Studio
3. Build and run on device/emulator
4. Grant storage permissions when prompted

## Usage

### First Launch
1. Grant storage permissions
2. App will automatically scan for PDF files
3. PDF files will be displayed in a list

### Opening PDFs
- Tap any PDF from the list to open
- Use the FAB (+) button to select files manually
- Files opened from other apps will launch directly

### PDF Viewer Controls
- **Pinch to zoom**: Zoom in/out
- **Swipe**: Navigate between pages
- **Menu options**:
  - Night mode toggle
  - Add bookmark
  - Share PDF
  - Zoom controls

### Bookmarks
- Tap bookmark icon while viewing a PDF
- Bookmarks are saved with page number
- Access bookmarks from the main menu

## File Structure

```
app/
├── src/main/java/com/pdfviewer/app/
│   ├── MainActivity.kt              # Main file browser
│   ├── ui/
│   │   └── PDFViewerActivity.kt     # PDF viewing screen
│   ├── model/
│   │   └── PDFFile.kt              # Data models
│   ├── database/
│   │   ├── PDFDatabase.kt          # Room database
│   │   └── PDFDao.kt               # Database access
│   ├── viewmodel/
│   │   ├── MainViewModel.kt        # Main screen logic
│   │   └── PDFViewerViewModel.kt   # Viewer screen logic
│   └── adapter/
│       └── PDFAdapter.kt           # RecyclerView adapter
└── src/main/res/
    ├── layout/                     # UI layouts
    ├── menu/                       # Menu definitions
    ├── values/                     # Strings, colors, themes
    └── xml/                        # File provider, backup rules
```

## Requirements

- **Minimum SDK**: Android 5.0 (API 21)
- **Target SDK**: Android 14 (API 34)
- **Permissions**: Storage access required
- **Storage**: Minimal app size, efficient memory usage

## Future Enhancements

- [ ] Text search within PDFs
- [ ] Annotation support
- [ ] Password-protected PDF support
- [ ] Cloud storage integration
- [ ] Thumbnail previews
- [ ] Reading progress tracking
- [ ] Multiple viewing modes
- [ ] Print functionality

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and feature requests, please create an issue in the repository.