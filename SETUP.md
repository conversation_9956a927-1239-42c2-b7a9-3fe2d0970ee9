# PDF Viewer Android App - Setup Guide

## Prerequisites

### 1. Install Android Studio
- Download from: https://developer.android.com/studio
- Install with default settings
- Launch Android Studio and complete the setup wizard

### 2. Install Android SDK
- Open Android Studio
- Go to Tools → SDK Manager
- Install:
  - Android SDK Platform 34 (Android 14)
  - Android SDK Build-Tools 34.0.0
  - Android SDK Platform-Tools
  - Android Emulator (if testing on emulator)

### 3. Set Environment Variables
Add to your system PATH:
```
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
C:\Users\<USER>\AppData\Local\Android\Sdk\tools
```

Set ANDROID_HOME:
```
ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
```

## Project Setup

### 1. Open Project in Android Studio
1. Launch Android Studio
2. Click "Open an existing project"
3. Navigate to the PDF project folder
4. Click "OK"

### 2. Sync Project
- Android Studio will automatically sync Gradle
- If not, click "Sync Now" in the notification bar
- Wait for sync to complete

### 3. Update local.properties
Edit `local.properties` file and set your SDK path:
```
sdk.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk
```

## Building the App

### Method 1: Using Android Studio
1. Open the project in Android Studio
2. Wait for Gradle sync to complete
3. Click Build → Build Bundle(s) / APK(s) → Build APK(s)
4. APK will be generated in `app/build/outputs/apk/debug/`

### Method 2: Using Command Line
1. Open Command Prompt in project directory
2. Run: `gradlew.bat assembleDebug`
3. APK will be generated in `app/build/outputs/apk/debug/`

### Method 3: Using Build Script
1. Double-click `build.bat`
2. Follow the on-screen instructions

## Installing the App

### On Physical Device
1. Enable Developer Options:
   - Go to Settings → About Phone
   - Tap "Build Number" 7 times
   - Go back to Settings → Developer Options
   - Enable "USB Debugging"

2. Connect device via USB
3. Run: `adb install app/build/outputs/apk/debug/app-debug.apk`

### On Emulator
1. Create an AVD in Android Studio:
   - Tools → AVD Manager
   - Create Virtual Device
   - Choose device and API level 21+
   - Start the emulator

2. Install APK:
   - Drag and drop APK to emulator, or
   - Run: `adb install app/build/outputs/apk/debug/app-debug.apk`

## Troubleshooting

### Common Issues

#### 1. Gradle Sync Failed
- Check internet connection
- Update Android Studio
- Invalidate caches: File → Invalidate Caches and Restart

#### 2. SDK Not Found
- Verify ANDROID_HOME is set correctly
- Update local.properties with correct SDK path
- Install required SDK components

#### 3. Build Failed
- Clean project: Build → Clean Project
- Rebuild: Build → Rebuild Project
- Check error messages in Build tab

#### 4. Permission Issues
- Grant storage permissions when app starts
- For Android 11+, enable "All files access" in settings

### Performance Tips
- Use physical device for better performance
- Close other applications while building
- Increase Gradle heap size in gradle.properties

## Development Setup

### For Code Editing
1. Install Android Studio plugins:
   - Kotlin (usually pre-installed)
   - Material Theme UI (optional)

2. Configure code style:
   - File → Settings → Editor → Code Style
   - Set to Kotlin official style

### For Testing
1. Create test PDF files in device storage
2. Test on different Android versions (API 21+)
3. Test with various PDF file sizes and types

## Project Structure Overview

```
pdf-viewer/
├── app/
│   ├── src/main/
│   │   ├── java/com/pdfviewer/app/
│   │   │   ├── MainActivity.kt           # Main file browser
│   │   │   ├── ui/PDFViewerActivity.kt   # PDF viewer
│   │   │   ├── model/                    # Data models
│   │   │   ├── database/                 # Room database
│   │   │   ├── viewmodel/                # ViewModels
│   │   │   └── adapter/                  # RecyclerView adapters
│   │   ├── res/                          # Resources
│   │   └── AndroidManifest.xml
│   └── build.gradle                      # App dependencies
├── build.gradle                          # Project configuration
├── settings.gradle                       # Project settings
└── README.md                            # Documentation
```

## Next Steps

1. Build and install the app
2. Grant required permissions
3. Add some PDF files to test
4. Explore the features:
   - File browsing
   - PDF viewing
   - Bookmarks
   - Night mode
   - Sharing

## Support

If you encounter issues:
1. Check the error logs in Android Studio
2. Verify all prerequisites are installed
3. Try cleaning and rebuilding the project
4. Check device compatibility (Android 5.0+)