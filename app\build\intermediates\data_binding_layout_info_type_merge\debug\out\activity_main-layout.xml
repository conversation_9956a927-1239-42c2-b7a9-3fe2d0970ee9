<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.pdfviewer.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="375" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="167" endOffset="53"/></Target><Target id="@+id/collapsingToolbar" view="com.google.android.material.appbar.CollapsingToolbarLayout"><Expressions/><location startLine="16" startOffset="8" endLine="165" endOffset="68"/></Target><Target id="@+id/textTotalPDFs" view="TextView"><Expressions/><location startLine="62" startOffset="24" endLine="69" endOffset="45"/></Target><Target id="@+id/textFavorites" view="TextView"><Expressions/><location startLine="98" startOffset="24" endLine="105" endOffset="44"/></Target><Target id="@+id/textCompleted" view="TextView"><Expressions/><location startLine="134" startOffset="24" endLine="141" endOffset="45"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="157" startOffset="12" endLine="163" endOffset="56"/></Target><Target id="@+id/textInputSearch" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="195" startOffset="20" endLine="212" endOffset="75"/></Target><Target id="@+id/editTextSearch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="205" startOffset="24" endLine="210" endOffset="50"/></Target><Target id="@+id/chipGroupFilters" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="215" startOffset="20" endLine="259" endOffset="64"/></Target><Target id="@+id/chipAll" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="223" startOffset="24" endLine="229" endOffset="48"/></Target><Target id="@+id/chipRecent" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="231" startOffset="24" endLine="236" endOffset="51"/></Target><Target id="@+id/chipFavorites" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="238" startOffset="24" endLine="243" endOffset="54"/></Target><Target id="@+id/chipBooks" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="245" startOffset="24" endLine="250" endOffset="50"/></Target><Target id="@+id/chipDocuments" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="252" startOffset="24" endLine="257" endOffset="54"/></Target><Target id="@+id/textSectionTitle" view="TextView"><Expressions/><location startLine="280" startOffset="20" endLine="288" endOffset="50"/></Target><Target id="@+id/buttonViewMode" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="290" startOffset="20" endLine="296" endOffset="59"/></Target><Target id="@+id/recyclerViewPdfs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="301" startOffset="16" endLine="307" endOffset="55"/></Target><Target id="@+id/layoutEmptyState" view="LinearLayout"><Expressions/><location startLine="310" startOffset="16" endLine="344" endOffset="30"/></Target><Target id="@+id/fabAddPdf" view="com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton"><Expressions/><location startLine="353" startOffset="4" endLine="362" endOffset="99"/></Target><Target id="@+id/progressLoading" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="365" startOffset="4" endLine="373" endOffset="43"/></Target></Targets></Layout>