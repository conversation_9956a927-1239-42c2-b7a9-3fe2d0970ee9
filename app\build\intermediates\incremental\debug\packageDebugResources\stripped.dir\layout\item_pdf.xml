<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:rippleColor="?attr/colorPrimary"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- PDF Thumbnail/Icon Container -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_gravity="center_vertical"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="?attr/colorPrimaryContainer">

            <ImageView
                android:id="@+id/imageViewThumbnail"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:contentDescription="PDF Icon"
                android:src="@drawable/ic_pdf_document"
                android:tint="?attr/colorOnPrimaryContainer" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Text Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewFileName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textAppearance="?attr/textAppearanceBodyLarge"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold"
                tools:text="Sample PDF Document with a Very Long Name" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewFileSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="2.5 MB" />

                <View
                    android:layout_width="4dp"
                    android:layout_height="4dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="8dp"
                    android:background="?attr/colorOnSurfaceVariant"
                    android:alpha="0.6" />

                <TextView
                    android:id="@+id/textViewFileDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="Dec 18, 2023" />

            </LinearLayout>

            <!-- Progress bar for reading progress (optional) -->
            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progressReading"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:visibility="gone"
                app:indicatorColor="?attr/colorPrimary"
                app:trackColor="?attr/colorSurfaceVariant"
                app:trackThickness="3dp"
                tools:progress="45"
                tools:visibility="visible" />

        </LinearLayout>

        <!-- Action Menu -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonMore"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:contentDescription="More options"
            app:icon="@drawable/ic_more_vert"
            app:iconTint="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>