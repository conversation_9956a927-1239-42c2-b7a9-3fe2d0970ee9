#!/usr/bin/env python3
"""
Create simple PDF icons using basic drawing
"""

def create_simple_png_data(size):
    """Create a simple PNG data structure (simplified approach)"""
    # This is a very basic approach - in a real scenario you'd use PIL or similar
    # For now, let's create the icon files with a placeholder approach
    
    # Create a simple bitmap representation
    data = []
    center = size // 2
    
    for y in range(size):
        row = []
        for x in range(size):
            # Create a circular background
            dist = ((x - center) ** 2 + (y - center) ** 2) ** 0.5
            if dist < center - 2:
                # Inside circle - purple background
                if (size//4 < x < 3*size//4) and (size//4 < y < 3*size//4):
                    # Document area - white
                    row.append((255, 255, 255, 255))
                else:
                    # Purple background
                    row.append((98, 0, 238, 255))
            else:
                # Outside circle - transparent
                row.append((0, 0, 0, 0))
        data.append(row)
    
    return data

def create_icon_file(size, filename):
    """Create an icon file with the given size"""
    print(f"Creating {filename} with size {size}x{size}")
    
    # For now, create a simple text file that describes the icon
    # In a real implementation, this would create actual PNG data
    with open(filename.replace('.png', '_info.txt'), 'w') as f:
        f.write(f"PDF Viewer Icon\n")
        f.write(f"Size: {size}x{size}\n")
        f.write(f"Description: Purple circular background with white PDF document\n")
        f.write(f"Colors: Background #6200EE, Document white, Text #6200EE\n")

# Icon sizes for different Android densities
sizes = {
    'app/src/main/res/mipmap-mdpi': 48,
    'app/src/main/res/mipmap-hdpi': 72,
    'app/src/main/res/mipmap-xhdpi': 96,
    'app/src/main/res/mipmap-xxhdpi': 144,
    'app/src/main/res/mipmap-xxxhdpi': 192
}

import os

for folder, size in sizes.items():
    os.makedirs(folder, exist_ok=True)
    create_icon_file(size, os.path.join(folder, 'ic_launcher.png'))
    create_icon_file(size, os.path.join(folder, 'ic_launcher_round.png'))

print("Icon templates created. Use an image editor to create actual PNG files based on the SVG template.")