package com.pdfviewer.app.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.pdfviewer.app.database.PDFDatabase
import com.pdfviewer.app.model.PDFCategory
import com.pdfviewer.app.model.PDFFile
import com.pdfviewer.app.repository.PDFRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: PDFRepository
    
    init {
        val database = PDFDatabase.getDatabase(application)
        repository = PDFRepository(database.pdfDao(), application)
    }
    
    // UI State
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    // PDF Lists
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    private val _selectedCategory = MutableStateFlow<PDFCategory?>(null)
    val selectedCategory: StateFlow<PDFCategory?> = _selectedCategory.asStateFlow()
    
    // Combined PDF list based on search and category filters
    val pdfFiles = combine(
        repository.getAllPDFs(),
        _searchQuery,
        _selectedCategory
    ) { allPDFs, query, category ->
        var filteredPDFs = allPDFs
        
        // Apply category filter
        if (category != null) {
            filteredPDFs = filteredPDFs.filter { it.category == category }
        }
        
        // Apply search filter
        if (query.isNotBlank()) {
            filteredPDFs = filteredPDFs.filter { 
                it.name.contains(query, ignoreCase = true) 
            }
        }
        
        filteredPDFs
    }.asLiveData()
    
    val recentPDFs = repository.getRecentPDFs().asLiveData()
    val favoritePDFs = repository.getFavoritePDFs().asLiveData()
    
    // Statistics
    private val _statistics = MutableLiveData<PDFStatistics>()
    val statistics: LiveData<PDFStatistics> = _statistics
    
    // Actions
    fun refreshPDFs() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val scannedFiles = repository.scanForPDFs()
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    lastScanCount = scannedFiles.size
                )
                updateStatistics()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to scan for PDFs: ${e.message}"
                )
            }
        }
    }
    
    fun searchPDFs(query: String) {
        _searchQuery.value = query
    }
    
    fun filterByCategory(category: PDFCategory?) {
        _selectedCategory.value = category
    }
    
    fun clearFilters() {
        _searchQuery.value = ""
        _selectedCategory.value = null
    }
    
    fun toggleFavorite(pdfFile: PDFFile) {
        viewModelScope.launch {
            try {
                repository.updateFavoriteStatus(pdfFile.path, !pdfFile.isFavorite)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update favorite: ${e.message}"
                )
            }
        }
    }
    
    fun deletePDF(pdfFile: PDFFile) {
        viewModelScope.launch {
            try {
                repository.deletePDFByPath(pdfFile.path)
                _uiState.value = _uiState.value.copy(
                    message = "PDF removed from library"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete PDF: ${e.message}"
                )
            }
        }
    }
    
    fun updateReadingProgress(pdfFile: PDFFile, progress: Int) {
        viewModelScope.launch {
            try {
                repository.updateReadingProgress(pdfFile.path, progress)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update progress: ${e.message}"
                )
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    private fun updateStatistics() {
        viewModelScope.launch {
            try {
                val stats = PDFStatistics(
                    totalPDFs = repository.getTotalPDFCount(),
                    favoritePDFs = repository.getFavoritePDFCount(),
                    startedPDFs = repository.getStartedPDFCount(),
                    completedPDFs = repository.getCompletedPDFCount(),
                    averageProgress = repository.getAverageReadingProgress()
                )
                _statistics.value = stats
            } catch (e: Exception) {
                // Handle statistics error silently
            }
        }
    }
    
    // Initialize data
    init {
        refreshPDFs()
    }
}

data class MainUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val lastScanCount: Int = 0
)

data class PDFStatistics(
    val totalPDFs: Int = 0,
    val favoritePDFs: Int = 0,
    val startedPDFs: Int = 0,
    val completedPDFs: Int = 0,
    val averageProgress: Double = 0.0
)