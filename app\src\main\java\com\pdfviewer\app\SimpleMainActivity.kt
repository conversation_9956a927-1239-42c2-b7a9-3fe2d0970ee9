package com.pdfviewer.app

import android.os.Bundle
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.pdfviewer.app.adapter.PDFAdapter
import com.pdfviewer.app.model.PDFFile

class SimpleMainActivity : AppCompatActivity() {
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var fab: FloatingActionButton
    private lateinit var pdfAdapter: PDFAdapter
    private val pdfFiles = mutableListOf<PDFFile>()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create layout programmatically to avoid any XML issues
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setBackgroundColor(android.graphics.Color.WHITE)
        }
        
        // Add title
        val titleText = TextView(this).apply {
            text = "PDF Viewer"
            textSize = 24f
            setPadding(32, 32, 32, 16)
            setTextColor(android.graphics.Color.BLACK)
        }
        mainLayout.addView(titleText)
        
        // Add test button
        val testButton = Button(this).apply {
            text = "Test Button - Click Me!"
            setOnClickListener {
                Toast.makeText(this@SimpleMainActivity, "Button clicked! App is working!", Toast.LENGTH_LONG).show()
                addSampleData()
            }
        }
        mainLayout.addView(testButton)
        
        // Add RecyclerView
        recyclerView = RecyclerView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
            setPadding(16, 16, 16, 16)
        }
        mainLayout.addView(recyclerView)
        
        setupRecyclerView()
        setContentView(mainLayout)
        
        Toast.makeText(this, "Simple PDF Viewer loaded!", Toast.LENGTH_SHORT).show()
    }
    
    private fun setupRecyclerView() {
        pdfAdapter = PDFAdapter(
            onItemClick = { pdfFile ->
                Toast.makeText(this, "Clicked: ${pdfFile.name}", Toast.LENGTH_SHORT).show()
            },
            onMoreClick = { pdfFile, view ->
                Toast.makeText(this, "More options for: ${pdfFile.name}", Toast.LENGTH_SHORT).show()
            }
        )
        
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@SimpleMainActivity)
            adapter = pdfAdapter
        }
    }
    
    private fun addSampleData() {
        pdfFiles.clear()
        
        pdfFiles.add(
            PDFFile(
                name = "Sample Document 1",
                path = "/storage/sample1.pdf",
                size = 1024 * 1024,
                lastModified = System.currentTimeMillis()
            )
        )
        
        pdfFiles.add(
            PDFFile(
                name = "Sample Document 2",
                path = "/storage/sample2.pdf",
                size = 2 * 1024 * 1024,
                lastModified = System.currentTimeMillis() - 86400000
            )
        )
        
        pdfFiles.add(
            PDFFile(
                name = "Sample Document 3",
                path = "/storage/sample3.pdf",
                size = 512 * 1024,
                lastModified = System.currentTimeMillis() - 172800000
            )
        )
        
        pdfAdapter.notifyDataSetChanged()
        Toast.makeText(this, "Added ${pdfFiles.size} sample PDFs to list", Toast.LENGTH_SHORT).show()
    }
}