package com.pdfviewer.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupMenu
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.android.material.button.MaterialButton
import com.google.android.material.progressindicator.LinearProgressIndicator
import com.pdfviewer.app.R
import com.pdfviewer.app.model.PDFFile

class PDFAdapter(
    private val onItemClick: (PDFFile) -> Unit,
    private val onMoreClick: (PDFFile, View) -> Unit = { _, _ -> }
) : ListAdapter<PDFFile, PDFAdapter.PDFViewHolder>(PDFDiffCallback()) {
    
    inner class PDFViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val imageViewThumbnail: ImageView = itemView.findViewById(R.id.imageViewThumbnail)
        private val textViewFileName: TextView = itemView.findViewById(R.id.textViewFileName)
        private val textViewFileSize: TextView = itemView.findViewById(R.id.textViewFileSize)
        private val textViewFileDate: TextView = itemView.findViewById(R.id.textViewFileDate)
        private val progressReading: LinearProgressIndicator = itemView.findViewById(R.id.progressReading)
        private val buttonMore: MaterialButton = itemView.findViewById(R.id.buttonMore)
        
        fun bind(pdfFile: PDFFile) {
            // Set file information
            textViewFileName.text = pdfFile.name
            textViewFileSize.text = pdfFile.getFormattedSize()
            textViewFileDate.text = pdfFile.getFormattedDate()
            
            // Load thumbnail or use default icon
            loadThumbnail(pdfFile)
            
            // Show reading progress if available
            setupReadingProgress(pdfFile)
            
            // Set click listeners
            itemView.setOnClickListener {
                onItemClick(pdfFile)
            }
            
            buttonMore.setOnClickListener { view ->
                showContextMenu(pdfFile, view)
            }
        }
        
        private fun loadThumbnail(pdfFile: PDFFile) {
            // For now, use the default PDF icon
            // In future updates, this will load actual PDF thumbnails
            Glide.with(itemView.context)
                .load(R.drawable.ic_pdf_document)
                .into(imageViewThumbnail)
        }
        
        private fun setupReadingProgress(pdfFile: PDFFile) {
            // Show reading progress if the file has been opened before
            val progress = pdfFile.readingProgress
            if (progress > 0) {
                progressReading.visibility = View.VISIBLE
                progressReading.progress = progress
            } else {
                progressReading.visibility = View.GONE
            }
        }
        
        private fun showContextMenu(pdfFile: PDFFile, anchorView: View) {
            val popup = PopupMenu(itemView.context, anchorView)
            popup.menuInflater.inflate(R.menu.menu_pdf_item, popup.menu)
            
            popup.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.action_open -> {
                        onItemClick(pdfFile)
                        true
                    }
                    R.id.action_share -> {
                        // Handle share action
                        onMoreClick(pdfFile, anchorView)
                        true
                    }
                    R.id.action_delete -> {
                        // Handle delete action
                        onMoreClick(pdfFile, anchorView)
                        true
                    }
                    R.id.action_info -> {
                        // Handle info action
                        onMoreClick(pdfFile, anchorView)
                        true
                    }
                    else -> false
                }
            }
            
            popup.show()
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PDFViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_pdf, parent, false)
        return PDFViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: PDFViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    // DiffUtil callback for efficient list updates
    private class PDFDiffCallback : DiffUtil.ItemCallback<PDFFile>() {
        override fun areItemsTheSame(oldItem: PDFFile, newItem: PDFFile): Boolean {
            return oldItem.path == newItem.path
        }
        
        override fun areContentsTheSame(oldItem: PDFFile, newItem: PDFFile): Boolean {
            return oldItem == newItem
        }
    }
}