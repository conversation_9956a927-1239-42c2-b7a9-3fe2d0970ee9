<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Background circle -->
    <path
        android:fillColor="#FF6200EE"
        android:pathData="M54,54m-50,0a50,50 0,1 1,100 0a50,50 0,1 1,-100 0" />
    
    <!-- PDF Document shape -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M30,25 L30,83 L78,83 L78,35 L68,25 Z" />
    
    <!-- Folded corner -->
    <path
        android:fillColor="#E0E0E0"
        android:pathData="M68,25 L68,35 L78,35 Z" />
    
    <!-- PDF Text -->
    <path
        android:fillColor="#FF6200EE"
        android:pathData="M35,45 L35,55 L40,55 Q43,55 43,52 Q43,49 40,49 L37,49 L37,45 Z M37,47 L37,51 L40,51 Q41,51 41,49 Q41,47 40,47 Z" />
    
    <path
        android:fillColor="#FF6200EE"
        android:pathData="M45,45 L45,55 L50,55 Q53,55 53,50 Q53,45 50,45 Z M47,47 L47,53 L50,53 Q51,53 51,50 Q51,47 50,47 Z" />
    
    <path
        android:fillColor="#FF6200EE"
        android:pathData="M55,45 L55,55 L57,55 L57,51 L61,51 L61,49 L57,49 L57,47 L62,47 L62,45 Z" />
    
    <!-- Document lines -->
    <path
        android:fillColor="#E0E0E0"
        android:pathData="M35,60 L70,60 M35,63 L65,63 M35,66 L68,66 M35,69 L62,69"
        android:strokeWidth="1"
        android:strokeColor="#E0E0E0" />
</vector>