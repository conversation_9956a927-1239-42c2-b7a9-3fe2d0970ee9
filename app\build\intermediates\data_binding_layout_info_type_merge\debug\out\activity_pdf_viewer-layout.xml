<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pdf_viewer" modulePackage="com.pdfviewer.app" filePath="app\src\main\res\layout\activity_pdf_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_pdf_viewer_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="382" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="57" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="58"/></Target><Target id="@+id/toolbarSearch" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="26" startOffset="8" endLine="55" endOffset="60"/></Target><Target id="@+id/textInputSearch" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="33" startOffset="12" endLine="53" endOffset="67"/></Target><Target id="@+id/editTextSearch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="16" endLine="51" endOffset="73"/></Target><Target id="@+id/imageViewPdf" view="ImageView"><Expressions/><location startLine="75" startOffset="16" endLine="81" endOffset="48"/></Target><Target id="@+id/layoutLoading" view="LinearLayout"><Expressions/><location startLine="88" startOffset="8" endLine="112" endOffset="22"/></Target><Target id="@+id/progressBar" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="97" startOffset="12" endLine="101" endOffset="46"/></Target><Target id="@+id/textLoadingStatus" view="TextView"><Expressions/><location startLine="103" startOffset="12" endLine="110" endOffset="58"/></Target><Target id="@+id/cardSearchResults" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="115" startOffset="8" endLine="175" endOffset="59"/></Target><Target id="@+id/textSearchResults" view="TextView"><Expressions/><location startLine="137" startOffset="20" endLine="144" endOffset="54"/></Target><Target id="@+id/buttonPrevResult" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="146" startOffset="20" endLine="152" endOffset="67"/></Target><Target id="@+id/buttonNextResult" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="154" startOffset="20" endLine="160" endOffset="69"/></Target><Target id="@+id/progressSearch" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="165" startOffset="16" endLine="171" endOffset="47"/></Target><Target id="@+id/cardTextSelection" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="178" startOffset="8" endLine="220" endOffset="59"/></Target><Target id="@+id/buttonCopy" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="194" startOffset="16" endLine="200" endOffset="50"/></Target><Target id="@+id/buttonShare" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="202" startOffset="16" endLine="208" endOffset="51"/></Target><Target id="@+id/buttonHighlight" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="210" startOffset="16" endLine="216" endOffset="55"/></Target><Target id="@+id/layoutBottomControls" view="LinearLayout"><Expressions/><location startLine="225" startOffset="4" endLine="369" endOffset="18"/></Target><Target id="@+id/buttonPrevPage" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="244" startOffset="12" endLine="250" endOffset="61"/></Target><Target id="@+id/textViewPageInfo" view="TextView"><Expressions/><location startLine="259" startOffset="16" endLine="266" endOffset="47"/></Target><Target id="@+id/sliderPage" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="269" startOffset="16" endLine="276" endOffset="37"/></Target><Target id="@+id/buttonNextPage" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="280" startOffset="12" endLine="286" endOffset="62"/></Target><Target id="@+id/buttonZoomOut" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="300" startOffset="12" endLine="306" endOffset="50"/></Target><Target id="@+id/textZoomLevel" view="TextView"><Expressions/><location startLine="309" startOffset="12" endLine="318" endOffset="35"/></Target><Target id="@+id/buttonZoomIn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="321" startOffset="12" endLine="327" endOffset="49"/></Target><Target id="@+id/buttonAutoScroll" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="336" startOffset="12" endLine="342" endOffset="52"/></Target><Target id="@+id/textScrollSpeed" view="TextView"><Expressions/><location startLine="345" startOffset="12" endLine="355" endOffset="33"/></Target><Target id="@+id/buttonReadingMode" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="358" startOffset="12" endLine="365" endOffset="52"/></Target><Target id="@+id/fabQuickActions" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="372" startOffset="4" endLine="380" endOffset="99"/></Target></Targets></Layout>