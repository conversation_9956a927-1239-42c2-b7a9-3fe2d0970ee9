<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".PDFViewerActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <!-- Main Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:layout_scrollFlags="scroll|enterAlways"
            app:menu="@menu/menu_pdf_viewer"
            app:navigationIcon="@drawable/ic_arrow_back" />

        <!-- Search Toolbar (Initially Hidden) -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbarSearch"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:visibility="gone"
            app:navigationIcon="@drawable/ic_close">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputSearch"
                style="@style/Widget.Material3.TextInputLayout.FilledBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:hint="Search in PDF..."
                app:boxBackgroundColor="@android:color/transparent"
                app:endIconDrawable="@drawable/ic_search"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextSearch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="?attr/colorOnSurface"
                    android:textColorHint="?attr/colorOnSurfaceVariant" />

            </com.google.android.material.textfield.TextInputLayout>

        </com.google.android.material.appbar.MaterialToolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- PDF Viewer with Zoom and Pan -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/colorSurface">
            
            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                
                <ImageView
                    android:id="@+id/imageViewPdf"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:contentDescription="PDF Page"
                    android:scaleType="matrix" />
                    
            </HorizontalScrollView>
            
        </ScrollView>

        <!-- Loading Progress -->
        <LinearLayout
            android:id="@+id/layoutLoading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/colorSurface"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true" />

            <TextView
                android:id="@+id/textLoadingStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Loading PDF..."
                android:textAppearance="?attr/textAppearanceBodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- Search Results Overlay -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardSearchResults"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_margin="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textSearchResults"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="5 results found" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonPrevResult"
                        style="@style/Widget.Material3.Button.IconButton"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:contentDescription="Previous result"
                        app:icon="@drawable/ic_keyboard_arrow_up" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonNextResult"
                        style="@style/Widget.Material3.Button.IconButton"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:contentDescription="Next result"
                        app:icon="@drawable/ic_keyboard_arrow_down" />

                </LinearLayout>

                <!-- Search Progress -->
                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:id="@+id/progressSearch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:indeterminate="true"
                    android:visibility="gone" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Text Selection Toolbar -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardTextSelection"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|top"
            android:layout_marginTop="80dp"
            android:visibility="gone"
            app:cardCornerRadius="24dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="8dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonCopy"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:contentDescription="Copy text"
                    app:icon="@drawable/ic_copy" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonShare"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:contentDescription="Share text"
                    app:icon="@drawable/ic_share" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonHighlight"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:contentDescription="Highlight text"
                    app:icon="@drawable/ic_highlight" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </FrameLayout>

    <!-- Bottom Controls -->
    <LinearLayout
        android:id="@+id/layoutBottomControls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="?attr/colorSurface"
        android:elevation="8dp"
        android:orientation="vertical"
        android:paddingVertical="8dp">

        <!-- Page Navigation -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonPrevPage"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="Previous page"
                app:icon="@drawable/ic_keyboard_arrow_left" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewPageInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:textStyle="bold"
                    tools:text="Page 1 of 10" />

                <!-- Page Slider -->
                <com.google.android.material.slider.Slider
                    android:id="@+id/sliderPage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:valueFrom="1"
                    android:valueTo="10"
                    tools:value="1" />

            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonNextPage"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="Next page"
                app:icon="@drawable/ic_keyboard_arrow_right" />

        </LinearLayout>

        <!-- Zoom and Auto-scroll Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp">

            <!-- Zoom Out -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonZoomOut"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="Zoom out"
                app:icon="@drawable/ic_zoom_out" />

            <!-- Zoom Level -->
            <TextView
                android:id="@+id/textZoomLevel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:minWidth="60dp"
                android:gravity="center"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:textColor="?attr/colorOnSurface"
                tools:text="100%" />

            <!-- Zoom In -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonZoomIn"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="Zoom in"
                app:icon="@drawable/ic_zoom_in" />

            <!-- Spacer -->
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Auto-scroll Toggle -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonAutoScroll"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="Auto-scroll"
                app:icon="@drawable/ic_play_arrow" />

            <!-- Auto-scroll Speed -->
            <TextView
                android:id="@+id/textScrollSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:minWidth="40dp"
                android:gravity="center"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:textColor="?attr/colorOnSurface"
                android:visibility="gone"
                tools:text="1x" />

            <!-- Reading Mode Toggle -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonReadingMode"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:contentDescription="Reading mode"
                app:icon="@drawable/ic_visibility" />

        </LinearLayout>

    </LinearLayout>

    <!-- Floating Action Button for Quick Actions -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabQuickActions"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|center_vertical"
        android:layout_margin="16dp"
        android:contentDescription="Quick actions"
        app:srcCompat="@drawable/ic_more_vert"
        app:layout_behavior="com.google.android.material.behavior.HideBottomViewOnScrollBehavior" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>