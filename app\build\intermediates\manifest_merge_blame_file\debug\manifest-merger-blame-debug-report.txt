1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pdfviewer.app.debug"
4    android:versionCode="2"
5    android:versionName="2.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:5:5-80
11-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:6:5-81
12-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:7:5-8:40
13-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:7:22-79
14    <uses-permission android:name="android.permission.INTERNET" />
14-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:9:5-67
14-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:9:22-64
15
16    <permission
16-->[androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.pdfviewer.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.pdfviewer.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:11:5-68:19
23        android:allowBackup="true"
23-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:12:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.9.0] C:\Gradle\gradle-8.4\caches\transforms-3\cda01deaece8a11159b1c59d9b483d75\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:13:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:14:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:15:9-43
30        android:label="@string/app_name"
30-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:16:9-41
31        android:requestLegacyExternalStorage="true"
31-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:20:9-52
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:17:9-54
33        android:supportsRtl="true"
33-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:18:9-35
34        android:theme="@style/Theme.PDFViewer" >
34-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:19:9-47
35
36        <!-- Main activity -->
37        <activity
37-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:24:9-40:20
38            android:name="com.pdfviewer.app.MainActivity"
38-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:25:13-41
39            android:exported="true"
39-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:26:13-36
40            android:theme="@style/Theme.PDFViewer.NoActionBar" >
40-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:27:13-63
41            <intent-filter>
41-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:28:13-31:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:29:17-69
42-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:29:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:30:17-77
44-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:30:27-74
45            </intent-filter>
46
47            <!-- Handle PDF files -->
48            <intent-filter>
48-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:34:13-39:29
49                <action android:name="android.intent.action.VIEW" />
49-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:35:17-69
49-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:35:25-66
50
51                <category android:name="android.intent.category.DEFAULT" />
51-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:36:17-76
51-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:36:27-73
52                <category android:name="android.intent.category.BROWSABLE" />
52-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:37:17-78
52-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:37:27-75
53
54                <data android:mimeType="application/pdf" />
54-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:38:17-60
54-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:38:23-57
55            </intent-filter>
56        </activity>
57
58        <!-- PDF Viewer Activity -->
59        <activity
59-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:43:9-51:20
60            android:name="com.pdfviewer.app.PDFViewerActivity"
60-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:44:13-46
61            android:exported="false"
61-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:45:13-37
62            android:parentActivityName="com.pdfviewer.app.MainActivity"
62-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:47:13-55
63            android:theme="@style/Theme.PDFViewer.NoActionBar" >
63-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:46:13-63
64            <meta-data
64-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:48:13-50:49
65                android:name="android.support.PARENT_ACTIVITY"
65-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:49:17-63
66                android:value=".MainActivity" />
66-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:50:17-46
67        </activity>
68
69        <!-- Simple test activity (backup) -->
70        <activity
70-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:54:9-57:66
71            android:name="com.pdfviewer.app.SimpleMainActivity"
71-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:55:13-47
72            android:exported="false"
72-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:56:13-37
73            android:theme="@style/Theme.PDFViewer.NoActionBar" />
73-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:57:13-63
74
75        <provider
76            android:name="androidx.core.content.FileProvider"
76-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:60:13-62
77            android:authorities="com.pdfviewer.app.debug.fileprovider"
77-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:61:13-64
78            android:exported="false"
78-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:62:13-37
79            android:grantUriPermissions="true" >
79-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:63:13-47
80            <meta-data
80-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:64:13-66:54
81                android:name="android.support.FILE_PROVIDER_PATHS"
81-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:65:17-67
82                android:resource="@xml/file_paths" />
82-->D:\apk apps\pdf\app\src\main\AndroidManifest.xml:66:17-51
83        </provider>
84        <provider
84-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
86            android:authorities="com.pdfviewer.app.debug.androidx-startup"
86-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
87            android:exported="false" >
87-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
88            <meta-data
88-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.emoji2.text.EmojiCompatInitializer"
89-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
90                android:value="androidx.startup" />
90-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
92-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
93                android:value="androidx.startup" />
93-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Gradle\gradle-8.4\caches\transforms-3\0e559ac84bccfef811d5ff275773776c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
94            <meta-data
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
96                android:value="androidx.startup" />
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
97        </provider>
98
99        <service
99-->[androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
100            android:name="androidx.room.MultiInstanceInvalidationService"
100-->[androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
101            android:directBootAware="true"
101-->[androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
102            android:exported="false" />
102-->[androidx.room:room-runtime:2.5.0] C:\Gradle\gradle-8.4\caches\transforms-3\55b7514228a52a33b0bab452c8728fc4\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
103
104        <receiver
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
105            android:name="androidx.profileinstaller.ProfileInstallReceiver"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
106            android:directBootAware="false"
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
107            android:enabled="true"
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
108            android:exported="true"
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
109            android:permission="android.permission.DUMP" >
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
111                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
114                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
117                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
118            </intent-filter>
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
120                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\transforms-3\ca010a0ffcbed2c7c8a10ed728d7775b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
121            </intent-filter>
122        </receiver>
123    </application>
124
125</manifest>
