@echo off
echo Verifying PDF Viewer Android Project Structure...
echo.

set ERROR_COUNT=0

REM Check essential files
echo Checking essential files...

if not exist "app\build.gradle" (
    echo ✗ Missing: app\build.gradle
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: app\build.gradle
)

if not exist "app\src\main\AndroidManifest.xml" (
    echo ✗ Missing: app\src\main\AndroidManifest.xml
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: app\src\main\AndroidManifest.xml
)

if not exist "app\src\main\java\com\pdfviewer\app\MainActivity.kt" (
    echo ✗ Missing: MainActivity.kt
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: MainActivity.kt
)

if not exist "app\src\main\java\com\pdfviewer\app\ui\PDFViewerActivity.kt" (
    echo ✗ Missing: PDFViewerActivity.kt
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: PDFViewerActivity.kt
)

echo.
echo Checking resource files...

if not exist "app\src\main\res\layout\activity_main.xml" (
    echo ✗ Missing: activity_main.xml
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: activity_main.xml
)

if not exist "app\src\main\res\layout\activity_pdf_viewer.xml" (
    echo ✗ Missing: activity_pdf_viewer.xml
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: activity_pdf_viewer.xml
)

if not exist "app\src\main\res\values\strings.xml" (
    echo ✗ Missing: strings.xml
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: strings.xml
)

echo.
echo Checking configuration files...

if not exist "build.gradle" (
    echo ✗ Missing: build.gradle
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: build.gradle
)

if not exist "settings.gradle" (
    echo ✗ Missing: settings.gradle
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: settings.gradle
)

if not exist "gradle.properties" (
    echo ✗ Missing: gradle.properties
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Found: gradle.properties
)

echo.
echo Project verification complete!

if %ERROR_COUNT% equ 0 (
    echo ✓ All essential files are present.
    echo ✓ Project structure is correct.
    echo.
    echo Ready to build! Run 'build.bat' or open in Android Studio.
) else (
    echo ✗ Found %ERROR_COUNT% missing files.
    echo Please check the project structure.
)

echo.
pause