plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
    id 'com.google.devtools.ksp' version '1.9.10-1.0.13'
}

android {
    namespace 'com.pdfviewer.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.pdfviewer.app"
        minSdk 24
        targetSdk 34
        versionCode 2
        versionName "2.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // Enable vector drawables support
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    
    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs += [
            "-opt-in=kotlin.RequiresOptIn",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
        ]
    }
    
    buildFeatures {
        viewBinding true
        buildConfig true
    }
    
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    // Core Android libraries - minimal set for compatibility
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.activity:activity-ktx:1.6.1'
    
    // Material Design
    implementation 'com.google.android.material:material:1.8.0'
    
    // Layout libraries
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    
    // Lifecycle components - older versions
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'
    
    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    
    // Room database - updated for Kotlin 1.9.10 compatibility with KSP
    implementation 'androidx.room:room-runtime:2.5.0'
    implementation 'androidx.room:room-ktx:2.5.0'
    ksp 'androidx.room:room-compiler:2.5.0'
    
    // Glide for image loading - updated version with KSP
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    ksp 'com.github.bumptech.glide:compiler:4.15.1'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}