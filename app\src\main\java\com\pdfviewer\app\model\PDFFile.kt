package com.pdfviewer.app.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.*

@Parcelize
data class PDFFile(
    val name: String,
    val path: String,
    val size: Long,
    val lastModified: Long,
    val pageCount: Int = 0,
    val readingProgress: Int = 0, // Progress percentage (0-100)
    val lastOpenedTime: Long = 0,
    val isFavorite: Boolean = false,
    val thumbnailPath: String? = null,
    val category: PDFCategory = PDFCategory.DOCUMENT
) : Parcelable {
    
    fun getFormattedSize(): String {
        val kb = size / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$size B"
        }
    }
    
    fun getFormattedDate(): String {
        val date = Date(lastModified)
        val formatter = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        return formatter.format(date)
    }
    
    fun getFormattedDateTime(): String {
        val date = Date(lastModified)
        val formatter = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        return formatter.format(date)
    }
    
    fun getLastOpenedFormatted(): String? {
        return if (lastOpenedTime > 0) {
            val date = Date(lastOpenedTime)
            val formatter = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
            formatter.format(date)
        } else null
    }
    
    fun getReadingProgressText(): String {
        return when {
            readingProgress == 0 -> "Not started"
            readingProgress == 100 -> "Completed"
            else -> "$readingProgress% read"
        }
    }
    
    fun isRecentlyOpened(): Boolean {
        val oneDayAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000)
        return lastOpenedTime > oneDayAgo
    }
    
    fun getFileExtension(): String {
        return name.substringAfterLast('.', "").lowercase()
    }
    
    fun isValidPDF(): Boolean {
        return getFileExtension() == "pdf"
    }
    
    // Create a copy with updated reading progress
    fun withProgress(progress: Int, currentPage: Int = 0): PDFFile {
        return copy(
            readingProgress = progress.coerceIn(0, 100),
            lastOpenedTime = System.currentTimeMillis()
        )
    }
    
    // Create a copy with favorite status
    fun withFavorite(favorite: Boolean): PDFFile {
        return copy(isFavorite = favorite)
    }
    
    // Create a copy with page count
    fun withPageCount(pages: Int): PDFFile {
        return copy(pageCount = pages)
    }
}

enum class PDFCategory(val displayName: String) {
    DOCUMENT("Documents"),
    BOOK("Books"),
    MAGAZINE("Magazines"),
    MANUAL("Manuals"),
    REPORT("Reports"),
    PRESENTATION("Presentations"),
    OTHER("Other");
    
    companion object {
        fun fromFileName(fileName: String): PDFCategory {
            val lowerName = fileName.lowercase()
            return when {
                lowerName.contains("book") || lowerName.contains("novel") -> BOOK
                lowerName.contains("magazine") || lowerName.contains("journal") -> MAGAZINE
                lowerName.contains("manual") || lowerName.contains("guide") -> MANUAL
                lowerName.contains("report") || lowerName.contains("analysis") -> REPORT
                lowerName.contains("presentation") || lowerName.contains("slide") -> PRESENTATION
                else -> DOCUMENT
            }
        }
    }
}