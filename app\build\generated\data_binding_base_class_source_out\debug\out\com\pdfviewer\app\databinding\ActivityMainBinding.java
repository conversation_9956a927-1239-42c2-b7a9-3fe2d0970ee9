// Generated by view binder compiler. Do not edit!
package com.pdfviewer.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pdfviewer.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final MaterialButton buttonViewMode;

  @NonNull
  public final Chip chipAll;

  @NonNull
  public final Chip chipBooks;

  @NonNull
  public final Chip chipDocuments;

  @NonNull
  public final Chip chipFavorites;

  @NonNull
  public final ChipGroup chipGroupFilters;

  @NonNull
  public final Chip chipRecent;

  @NonNull
  public final CollapsingToolbarLayout collapsingToolbar;

  @NonNull
  public final TextInputEditText editTextSearch;

  @NonNull
  public final ExtendedFloatingActionButton fabAddPdf;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final LinearProgressIndicator progressLoading;

  @NonNull
  public final RecyclerView recyclerViewPdfs;

  @NonNull
  public final TextView textCompleted;

  @NonNull
  public final TextView textFavorites;

  @NonNull
  public final TextInputLayout textInputSearch;

  @NonNull
  public final TextView textSectionTitle;

  @NonNull
  public final TextView textTotalPDFs;

  @NonNull
  public final MaterialToolbar toolbar;

  private ActivityMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull MaterialButton buttonViewMode,
      @NonNull Chip chipAll, @NonNull Chip chipBooks, @NonNull Chip chipDocuments,
      @NonNull Chip chipFavorites, @NonNull ChipGroup chipGroupFilters, @NonNull Chip chipRecent,
      @NonNull CollapsingToolbarLayout collapsingToolbar, @NonNull TextInputEditText editTextSearch,
      @NonNull ExtendedFloatingActionButton fabAddPdf, @NonNull LinearLayout layoutEmptyState,
      @NonNull LinearProgressIndicator progressLoading, @NonNull RecyclerView recyclerViewPdfs,
      @NonNull TextView textCompleted, @NonNull TextView textFavorites,
      @NonNull TextInputLayout textInputSearch, @NonNull TextView textSectionTitle,
      @NonNull TextView textTotalPDFs, @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.buttonViewMode = buttonViewMode;
    this.chipAll = chipAll;
    this.chipBooks = chipBooks;
    this.chipDocuments = chipDocuments;
    this.chipFavorites = chipFavorites;
    this.chipGroupFilters = chipGroupFilters;
    this.chipRecent = chipRecent;
    this.collapsingToolbar = collapsingToolbar;
    this.editTextSearch = editTextSearch;
    this.fabAddPdf = fabAddPdf;
    this.layoutEmptyState = layoutEmptyState;
    this.progressLoading = progressLoading;
    this.recyclerViewPdfs = recyclerViewPdfs;
    this.textCompleted = textCompleted;
    this.textFavorites = textFavorites;
    this.textInputSearch = textInputSearch;
    this.textSectionTitle = textSectionTitle;
    this.textTotalPDFs = textTotalPDFs;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.buttonViewMode;
      MaterialButton buttonViewMode = ViewBindings.findChildViewById(rootView, id);
      if (buttonViewMode == null) {
        break missingId;
      }

      id = R.id.chipAll;
      Chip chipAll = ViewBindings.findChildViewById(rootView, id);
      if (chipAll == null) {
        break missingId;
      }

      id = R.id.chipBooks;
      Chip chipBooks = ViewBindings.findChildViewById(rootView, id);
      if (chipBooks == null) {
        break missingId;
      }

      id = R.id.chipDocuments;
      Chip chipDocuments = ViewBindings.findChildViewById(rootView, id);
      if (chipDocuments == null) {
        break missingId;
      }

      id = R.id.chipFavorites;
      Chip chipFavorites = ViewBindings.findChildViewById(rootView, id);
      if (chipFavorites == null) {
        break missingId;
      }

      id = R.id.chipGroupFilters;
      ChipGroup chipGroupFilters = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupFilters == null) {
        break missingId;
      }

      id = R.id.chipRecent;
      Chip chipRecent = ViewBindings.findChildViewById(rootView, id);
      if (chipRecent == null) {
        break missingId;
      }

      id = R.id.collapsingToolbar;
      CollapsingToolbarLayout collapsingToolbar = ViewBindings.findChildViewById(rootView, id);
      if (collapsingToolbar == null) {
        break missingId;
      }

      id = R.id.editTextSearch;
      TextInputEditText editTextSearch = ViewBindings.findChildViewById(rootView, id);
      if (editTextSearch == null) {
        break missingId;
      }

      id = R.id.fabAddPdf;
      ExtendedFloatingActionButton fabAddPdf = ViewBindings.findChildViewById(rootView, id);
      if (fabAddPdf == null) {
        break missingId;
      }

      id = R.id.layoutEmptyState;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.progressLoading;
      LinearProgressIndicator progressLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressLoading == null) {
        break missingId;
      }

      id = R.id.recyclerViewPdfs;
      RecyclerView recyclerViewPdfs = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPdfs == null) {
        break missingId;
      }

      id = R.id.textCompleted;
      TextView textCompleted = ViewBindings.findChildViewById(rootView, id);
      if (textCompleted == null) {
        break missingId;
      }

      id = R.id.textFavorites;
      TextView textFavorites = ViewBindings.findChildViewById(rootView, id);
      if (textFavorites == null) {
        break missingId;
      }

      id = R.id.textInputSearch;
      TextInputLayout textInputSearch = ViewBindings.findChildViewById(rootView, id);
      if (textInputSearch == null) {
        break missingId;
      }

      id = R.id.textSectionTitle;
      TextView textSectionTitle = ViewBindings.findChildViewById(rootView, id);
      if (textSectionTitle == null) {
        break missingId;
      }

      id = R.id.textTotalPDFs;
      TextView textTotalPDFs = ViewBindings.findChildViewById(rootView, id);
      if (textTotalPDFs == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((CoordinatorLayout) rootView, appBarLayout, buttonViewMode,
          chipAll, chipBooks, chipDocuments, chipFavorites, chipGroupFilters, chipRecent,
          collapsingToolbar, editTextSearch, fabAddPdf, layoutEmptyState, progressLoading,
          recyclerViewPdfs, textCompleted, textFavorites, textInputSearch, textSectionTitle,
          textTotalPDFs, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
