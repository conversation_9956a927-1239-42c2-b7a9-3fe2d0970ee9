@echo off
echo Creating PDF Viewer App Icons...
echo.

REM Create directories if they don't exist
mkdir "app\src\main\res\mipmap-mdpi" 2>nul
mkdir "app\src\main\res\mipmap-hdpi" 2>nul
mkdir "app\src\main\res\mipmap-xhdpi" 2>nul
mkdir "app\src\main\res\mipmap-xxhdpi" 2>nul
mkdir "app\src\main\res\mipmap-xxxhdpi" 2>nul

echo Icon directories created.
echo.
echo To complete the icon creation:
echo 1. Use the icon_template.svg file as a reference
echo 2. Create PNG files in the following sizes:
echo    - mipmap-mdpi: 48x48 pixels
echo    - mipmap-hdpi: 72x72 pixels  
echo    - mipmap-xhdpi: 96x96 pixels
echo    - mipmap-xxhdpi: 144x144 pixels
echo    - mipmap-xxxhdpi: 192x192 pixels
echo.
echo 3. Name them ic_launcher.png and ic_launcher_round.png in each folder
echo.
echo Alternatively, you can use online icon generators like:
echo - https://romannurik.github.io/AndroidAssetStudio/
echo - https://appicon.co/
echo.

REM Copy existing icons as templates (if they exist)
if exist "app\src\main\res\mipmap-hdpi\ic_launcher.png" (
    echo Copying existing icons as templates...
    copy "app\src\main\res\mipmap-hdpi\ic_launcher.png" "app\src\main\res\mipmap-mdpi\ic_launcher.png" >nul 2>&1
    copy "app\src\main\res\mipmap-hdpi\ic_launcher.png" "app\src\main\res\mipmap-xhdpi\ic_launcher.png" >nul 2>&1
    copy "app\src\main\res\mipmap-hdpi\ic_launcher.png" "app\src\main\res\mipmap-xxhdpi\ic_launcher.png" >nul 2>&1
    copy "app\src\main\res\mipmap-hdpi\ic_launcher.png" "app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" >nul 2>&1
    
    copy "app\src\main\res\mipmap-hdpi\ic_launcher_round.png" "app\src\main\res\mipmap-mdpi\ic_launcher_round.png" >nul 2>&1
    copy "app\src\main\res\mipmap-hdpi\ic_launcher_round.png" "app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" >nul 2>&1
    copy "app\src\main\res\mipmap-hdpi\ic_launcher_round.png" "app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" >nul 2>&1
    copy "app\src\main\res\mipmap-hdpi\ic_launcher_round.png" "app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" >nul 2>&1
    
    echo Template icons copied to all density folders.
)

echo.
echo Icon creation setup complete!
pause