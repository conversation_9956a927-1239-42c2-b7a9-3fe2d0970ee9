// Generated by view binder compiler. Do not edit!
package com.pdfviewer.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.slider.Slider;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pdfviewer.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPdfViewerBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final MaterialButton buttonAutoScroll;

  @NonNull
  public final MaterialButton buttonCopy;

  @NonNull
  public final MaterialButton buttonHighlight;

  @NonNull
  public final MaterialButton buttonNextPage;

  @NonNull
  public final MaterialButton buttonNextResult;

  @NonNull
  public final MaterialButton buttonPrevPage;

  @NonNull
  public final MaterialButton buttonPrevResult;

  @NonNull
  public final MaterialButton buttonReadingMode;

  @NonNull
  public final MaterialButton buttonShare;

  @NonNull
  public final MaterialButton buttonZoomIn;

  @NonNull
  public final MaterialButton buttonZoomOut;

  @NonNull
  public final MaterialCardView cardSearchResults;

  @NonNull
  public final MaterialCardView cardTextSelection;

  @NonNull
  public final TextInputEditText editTextSearch;

  @NonNull
  public final FloatingActionButton fabQuickActions;

  @NonNull
  public final ImageView imageViewPdf;

  @NonNull
  public final LinearLayout layoutBottomControls;

  @NonNull
  public final LinearLayout layoutLoading;

  @NonNull
  public final CircularProgressIndicator progressBar;

  @NonNull
  public final LinearProgressIndicator progressSearch;

  @NonNull
  public final Slider sliderPage;

  @NonNull
  public final TextInputLayout textInputSearch;

  @NonNull
  public final TextView textLoadingStatus;

  @NonNull
  public final TextView textScrollSpeed;

  @NonNull
  public final TextView textSearchResults;

  @NonNull
  public final TextView textViewPageInfo;

  @NonNull
  public final TextView textZoomLevel;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final MaterialToolbar toolbarSearch;

  private ActivityPdfViewerBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull MaterialButton buttonAutoScroll,
      @NonNull MaterialButton buttonCopy, @NonNull MaterialButton buttonHighlight,
      @NonNull MaterialButton buttonNextPage, @NonNull MaterialButton buttonNextResult,
      @NonNull MaterialButton buttonPrevPage, @NonNull MaterialButton buttonPrevResult,
      @NonNull MaterialButton buttonReadingMode, @NonNull MaterialButton buttonShare,
      @NonNull MaterialButton buttonZoomIn, @NonNull MaterialButton buttonZoomOut,
      @NonNull MaterialCardView cardSearchResults, @NonNull MaterialCardView cardTextSelection,
      @NonNull TextInputEditText editTextSearch, @NonNull FloatingActionButton fabQuickActions,
      @NonNull ImageView imageViewPdf, @NonNull LinearLayout layoutBottomControls,
      @NonNull LinearLayout layoutLoading, @NonNull CircularProgressIndicator progressBar,
      @NonNull LinearProgressIndicator progressSearch, @NonNull Slider sliderPage,
      @NonNull TextInputLayout textInputSearch, @NonNull TextView textLoadingStatus,
      @NonNull TextView textScrollSpeed, @NonNull TextView textSearchResults,
      @NonNull TextView textViewPageInfo, @NonNull TextView textZoomLevel,
      @NonNull MaterialToolbar toolbar, @NonNull MaterialToolbar toolbarSearch) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.buttonAutoScroll = buttonAutoScroll;
    this.buttonCopy = buttonCopy;
    this.buttonHighlight = buttonHighlight;
    this.buttonNextPage = buttonNextPage;
    this.buttonNextResult = buttonNextResult;
    this.buttonPrevPage = buttonPrevPage;
    this.buttonPrevResult = buttonPrevResult;
    this.buttonReadingMode = buttonReadingMode;
    this.buttonShare = buttonShare;
    this.buttonZoomIn = buttonZoomIn;
    this.buttonZoomOut = buttonZoomOut;
    this.cardSearchResults = cardSearchResults;
    this.cardTextSelection = cardTextSelection;
    this.editTextSearch = editTextSearch;
    this.fabQuickActions = fabQuickActions;
    this.imageViewPdf = imageViewPdf;
    this.layoutBottomControls = layoutBottomControls;
    this.layoutLoading = layoutLoading;
    this.progressBar = progressBar;
    this.progressSearch = progressSearch;
    this.sliderPage = sliderPage;
    this.textInputSearch = textInputSearch;
    this.textLoadingStatus = textLoadingStatus;
    this.textScrollSpeed = textScrollSpeed;
    this.textSearchResults = textSearchResults;
    this.textViewPageInfo = textViewPageInfo;
    this.textZoomLevel = textZoomLevel;
    this.toolbar = toolbar;
    this.toolbarSearch = toolbarSearch;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPdfViewerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPdfViewerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_pdf_viewer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPdfViewerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.buttonAutoScroll;
      MaterialButton buttonAutoScroll = ViewBindings.findChildViewById(rootView, id);
      if (buttonAutoScroll == null) {
        break missingId;
      }

      id = R.id.buttonCopy;
      MaterialButton buttonCopy = ViewBindings.findChildViewById(rootView, id);
      if (buttonCopy == null) {
        break missingId;
      }

      id = R.id.buttonHighlight;
      MaterialButton buttonHighlight = ViewBindings.findChildViewById(rootView, id);
      if (buttonHighlight == null) {
        break missingId;
      }

      id = R.id.buttonNextPage;
      MaterialButton buttonNextPage = ViewBindings.findChildViewById(rootView, id);
      if (buttonNextPage == null) {
        break missingId;
      }

      id = R.id.buttonNextResult;
      MaterialButton buttonNextResult = ViewBindings.findChildViewById(rootView, id);
      if (buttonNextResult == null) {
        break missingId;
      }

      id = R.id.buttonPrevPage;
      MaterialButton buttonPrevPage = ViewBindings.findChildViewById(rootView, id);
      if (buttonPrevPage == null) {
        break missingId;
      }

      id = R.id.buttonPrevResult;
      MaterialButton buttonPrevResult = ViewBindings.findChildViewById(rootView, id);
      if (buttonPrevResult == null) {
        break missingId;
      }

      id = R.id.buttonReadingMode;
      MaterialButton buttonReadingMode = ViewBindings.findChildViewById(rootView, id);
      if (buttonReadingMode == null) {
        break missingId;
      }

      id = R.id.buttonShare;
      MaterialButton buttonShare = ViewBindings.findChildViewById(rootView, id);
      if (buttonShare == null) {
        break missingId;
      }

      id = R.id.buttonZoomIn;
      MaterialButton buttonZoomIn = ViewBindings.findChildViewById(rootView, id);
      if (buttonZoomIn == null) {
        break missingId;
      }

      id = R.id.buttonZoomOut;
      MaterialButton buttonZoomOut = ViewBindings.findChildViewById(rootView, id);
      if (buttonZoomOut == null) {
        break missingId;
      }

      id = R.id.cardSearchResults;
      MaterialCardView cardSearchResults = ViewBindings.findChildViewById(rootView, id);
      if (cardSearchResults == null) {
        break missingId;
      }

      id = R.id.cardTextSelection;
      MaterialCardView cardTextSelection = ViewBindings.findChildViewById(rootView, id);
      if (cardTextSelection == null) {
        break missingId;
      }

      id = R.id.editTextSearch;
      TextInputEditText editTextSearch = ViewBindings.findChildViewById(rootView, id);
      if (editTextSearch == null) {
        break missingId;
      }

      id = R.id.fabQuickActions;
      FloatingActionButton fabQuickActions = ViewBindings.findChildViewById(rootView, id);
      if (fabQuickActions == null) {
        break missingId;
      }

      id = R.id.imageViewPdf;
      ImageView imageViewPdf = ViewBindings.findChildViewById(rootView, id);
      if (imageViewPdf == null) {
        break missingId;
      }

      id = R.id.layoutBottomControls;
      LinearLayout layoutBottomControls = ViewBindings.findChildViewById(rootView, id);
      if (layoutBottomControls == null) {
        break missingId;
      }

      id = R.id.layoutLoading;
      LinearLayout layoutLoading = ViewBindings.findChildViewById(rootView, id);
      if (layoutLoading == null) {
        break missingId;
      }

      id = R.id.progressBar;
      CircularProgressIndicator progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progressSearch;
      LinearProgressIndicator progressSearch = ViewBindings.findChildViewById(rootView, id);
      if (progressSearch == null) {
        break missingId;
      }

      id = R.id.sliderPage;
      Slider sliderPage = ViewBindings.findChildViewById(rootView, id);
      if (sliderPage == null) {
        break missingId;
      }

      id = R.id.textInputSearch;
      TextInputLayout textInputSearch = ViewBindings.findChildViewById(rootView, id);
      if (textInputSearch == null) {
        break missingId;
      }

      id = R.id.textLoadingStatus;
      TextView textLoadingStatus = ViewBindings.findChildViewById(rootView, id);
      if (textLoadingStatus == null) {
        break missingId;
      }

      id = R.id.textScrollSpeed;
      TextView textScrollSpeed = ViewBindings.findChildViewById(rootView, id);
      if (textScrollSpeed == null) {
        break missingId;
      }

      id = R.id.textSearchResults;
      TextView textSearchResults = ViewBindings.findChildViewById(rootView, id);
      if (textSearchResults == null) {
        break missingId;
      }

      id = R.id.textViewPageInfo;
      TextView textViewPageInfo = ViewBindings.findChildViewById(rootView, id);
      if (textViewPageInfo == null) {
        break missingId;
      }

      id = R.id.textZoomLevel;
      TextView textZoomLevel = ViewBindings.findChildViewById(rootView, id);
      if (textZoomLevel == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.toolbarSearch;
      MaterialToolbar toolbarSearch = ViewBindings.findChildViewById(rootView, id);
      if (toolbarSearch == null) {
        break missingId;
      }

      return new ActivityPdfViewerBinding((CoordinatorLayout) rootView, appBarLayout,
          buttonAutoScroll, buttonCopy, buttonHighlight, buttonNextPage, buttonNextResult,
          buttonPrevPage, buttonPrevResult, buttonReadingMode, buttonShare, buttonZoomIn,
          buttonZoomOut, cardSearchResults, cardTextSelection, editTextSearch, fabQuickActions,
          imageViewPdf, layoutBottomControls, layoutLoading, progressBar, progressSearch,
          sliderPage, textInputSearch, textLoadingStatus, textScrollSpeed, textSearchResults,
          textViewPageInfo, textZoomLevel, toolbar, toolbarSearch);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
