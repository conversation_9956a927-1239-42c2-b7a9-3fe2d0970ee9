<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="bookmark_color">#FF9800</color>
    <color name="gradient_end">#EADDFF</color>
    <color name="gradient_start">#6750A4</color>
    <color name="highlight_color">#FFEB3B</color>
    <color name="info_color">#2196F3</color>
    <color name="md_theme_error">#BA1A1A</color>
    <color name="md_theme_error_container">#FFDAD6</color>
    <color name="md_theme_on_error">#FFFFFF</color>
    <color name="md_theme_on_error_container">#410002</color>
    <color name="md_theme_on_primary">#FFFFFF</color>
    <color name="md_theme_on_primary_container">#21005D</color>
    <color name="md_theme_on_secondary">#FFFFFF</color>
    <color name="md_theme_on_secondary_container">#1D192B</color>
    <color name="md_theme_on_surface">#1C1B1F</color>
    <color name="md_theme_on_surface_variant">#49454F</color>
    <color name="md_theme_on_tertiary">#FFFFFF</color>
    <color name="md_theme_on_tertiary_container">#31111D</color>
    <color name="md_theme_outline">#79747E</color>
    <color name="md_theme_outline_variant">#CAC4D0</color>
    <color name="md_theme_primary">#6750A4</color>
    <color name="md_theme_primary_container">#EADDFF</color>
    <color name="md_theme_secondary">#625B71</color>
    <color name="md_theme_secondary_container">#E8DEF8</color>
    <color name="md_theme_surface">#FFFBFE</color>
    <color name="md_theme_surface_variant">#E7E0EC</color>
    <color name="md_theme_tertiary">#7D5260</color>
    <color name="md_theme_tertiary_container">#FFD8E4</color>
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#80FFFFFF</color>
    <color name="pdf_background">#FAFAFA</color>
    <color name="pdf_page_shadow">#1A000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="reading_progress">#4CAF50</color>
    <color name="success_color">#4CAF50</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="warning_color">#FF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="action_settings">Settings</string>
    <string name="app_name">PDF Viewer</string>
    <string name="first_fragment_label">First Fragment</string>
    <string name="lorem_ipsum">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris
        volutpat, dolor id interdum ullamcorper, risus dolor egestas lectus, sit amet mattis purus
        dui nec risus. Maecenas non sodales nisi, vel dictum dolor. Class aptent taciti sociosqu ad
        litora torquent per conubia nostra, per inceptos himenaeos. Suspendisse blandit eleifend
        diam, vel rutrum tellus vulputate quis. Aliquam eget libero aliquet, imperdiet nisl a,
        ornare ex. Sed rhoncus est ut libero porta lobortis. Fusce in dictum tellus.\n\n
        Suspendisse interdum ornare ante. Aliquam nec cursus lorem. Morbi id magna felis. Vivamus
        egestas, est a condimentum egestas, turpis nisl iaculis ipsum, in dictum tellus dolor sed
        nunc. Aliquam erat volutpat. Cras egestas accumsan lorem, vel tincidunt dui efficitur
        vitae. Nullam vehicula dolor sed mi congue finibus. Donec ut leo congue, dapibus elit
        eget, vulputate mauris. Ut tempor semper lorem, ut tincidunt tortor imperdiet ut. Sed
        sagittis urna a commodo fermentum. Proin fringilla volutpat justo, vel imperdiet ex
        imperdiet in. Maecenas consectetur, purus a posuere imperdiet, lectus magna vehicula magna,
        eget facilisis massa turpis sit amet lorem. Maecenas consectetur, purus a posuere
        imperdiet, lectus magna vehicula magna, eget facilisis massa turpis sit amet lorem.
    </string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>
    <string name="second_fragment_label">Second Fragment</string>
    <style name="ShapeAppearance.PDFViewer.BottomSheet" parent="ShapeAppearance.Material3.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="ShapeAppearance.PDFViewer.SmallComponent" parent="ShapeAppearance.Material3.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>
    <style name="TextAppearance.PDFViewer.Body" parent="TextAppearance.Material3.BodyLarge">
    </style>
    <style name="TextAppearance.PDFViewer.Headline" parent="TextAppearance.Material3.HeadlineMedium">
    </style>
    <style name="Theme.PDFViewer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_on_primary</item>
        <item name="colorPrimaryContainer">@color/md_theme_primary_container</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_on_primary_container</item>
        
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_on_secondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_on_secondary_container</item>
        
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_on_tertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiary_container</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_on_tertiary_container</item>
        
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_on_error</item>
        <item name="colorErrorContainer">@color/md_theme_error_container</item>
        <item name="colorOnErrorContainer">@color/md_theme_on_error_container</item>
        
        <item name="colorSurface">@color/md_theme_surface</item>
        <item name="colorOnSurface">@color/md_theme_on_surface</item>
        <item name="colorSurfaceVariant">@color/md_theme_surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_on_surface_variant</item>
        
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outline_variant</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="o_mr1">true</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
    </style>
    <style name="Theme.PDFViewer.BottomSheetDialog" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.PDFViewer.BottomSheet</item>
    </style>
    <style name="Theme.PDFViewer.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <style name="Theme.PDFViewer.SplashScreen" parent="Theme.PDFViewer">
        <item name="android:windowBackground">@color/md_theme_surface</item>
    </style>
    <style name="Widget.PDFViewer.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearance">@style/ShapeAppearance.PDFViewer.BottomSheet</item>
    </style>
    <style name="Widget.PDFViewer.Button" parent="Widget.Material3.Button">
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="Widget.PDFViewer.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="Widget.PDFViewer.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
        <item name="rippleColor">?attr/colorPrimary</item>
    </style>
    <style name="Widget.PDFViewer.FloatingActionButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="shapeAppearance">@style/ShapeAppearance.PDFViewer.SmallComponent</item>
    </style>
</resources>