{"logs": [{"outputFile": "com.pdfviewer.app-mergeDebugResources-40:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\763e7e3c7daae90be5c167cae26ed6a3\\transformed\\material-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2425,2475,2526,2592,2656,2725,2803,2864,2935,3002,3062,3141,3208,3291,3376,3450,3515,3591,3639,3703,3779,3857,3919,3983,4046,4126,4202,4280,4357,4411,4466", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,78,49,50,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2420,2470,2521,2587,2651,2720,2798,2859,2930,2997,3057,3136,3203,3286,3371,3445,3510,3586,3634,3698,3774,3852,3914,3978,4041,4121,4197,4275,4352,4406,4461,4530"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2854,2918,2980,3050,3120,3197,3288,3394,3467,3529,3606,3665,3724,3802,3863,3920,3976,4035,4093,4147,4232,4288,4346,4400,4465,4557,4631,4707,4829,4891,4953,5032,5082,5133,5199,5263,5332,5410,5471,5542,5609,5669,5748,5815,5898,5983,6057,6122,6198,6246,6310,6386,6464,6526,6590,6653,6733,6809,6887,6964,7018,7073", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,78,49,50,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,53,54,68", "endOffsets": "292,2913,2975,3045,3115,3192,3283,3389,3462,3524,3601,3660,3719,3797,3858,3915,3971,4030,4088,4142,4227,4283,4341,4395,4460,4552,4626,4702,4824,4886,4948,5027,5077,5128,5194,5258,5327,5405,5466,5537,5604,5664,5743,5810,5893,5978,6052,6117,6193,6241,6305,6381,6459,6521,6585,6648,6728,6804,6882,6959,7013,7068,7137"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,7142", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,7216"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\cda01deaece8a11159b1c59d9b483d75\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "7221", "endColumns": "100", "endOffsets": "7317"}}]}]}