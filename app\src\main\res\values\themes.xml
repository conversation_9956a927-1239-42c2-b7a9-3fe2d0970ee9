<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme with Material Design 3 -->
    <style name="Theme.PDFViewer" parent="Theme.Material3.DayNight">
        <!-- Material Design 3 Color System -->
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_on_primary</item>
        <item name="colorPrimaryContainer">@color/md_theme_primary_container</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_on_primary_container</item>
        
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_on_secondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_on_secondary_container</item>
        
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_on_tertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiary_container</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_on_tertiary_container</item>
        
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_on_error</item>
        <item name="colorErrorContainer">@color/md_theme_error_container</item>
        <item name="colorOnErrorContainer">@color/md_theme_on_error_container</item>
        
        <item name="colorSurface">@color/md_theme_surface</item>
        <item name="colorOnSurface">@color/md_theme_on_surface</item>
        <item name="colorSurfaceVariant">@color/md_theme_surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_on_surface_variant</item>
        
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outline_variant</item>
        
        <!-- Status bar and navigation bar -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o_mr1">true</item>
        
        <!-- Enable edge-to-edge -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceStatusBarContrast" tools:targetApi="q">false</item>
        <item name="android:enforceNavigationBarContrast" tools:targetApi="q">false</item>
    </style>

    <!-- No Action Bar theme for full-screen activities -->
    <style name="Theme.PDFViewer.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.PDFViewer.SplashScreen" parent="Theme.PDFViewer">
        <item name="android:windowBackground">@color/md_theme_surface</item>
    </style>

    <!-- Bottom Sheet Theme -->
    <style name="Theme.PDFViewer.BottomSheetDialog" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.PDFViewer.BottomSheet</item>
    </style>

    <!-- Custom Bottom Sheet Style -->
    <style name="Widget.PDFViewer.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearance">@style/ShapeAppearance.PDFViewer.BottomSheet</item>
    </style>

    <!-- Bottom Sheet Shape -->
    <style name="ShapeAppearance.PDFViewer.BottomSheet" parent="ShapeAppearance.Material3.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>

    <!-- Card Styles -->
    <style name="Widget.PDFViewer.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
        <item name="rippleColor">?attr/colorPrimary</item>
    </style>

    <!-- Button Styles -->
    <style name="Widget.PDFViewer.Button" parent="Widget.Material3.Button">
        <item name="cornerRadius">8dp</item>
    </style>

    <style name="Widget.PDFViewer.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="cornerRadius">8dp</item>
    </style>

    <!-- FAB Style -->
    <style name="Widget.PDFViewer.FloatingActionButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="shapeAppearance">@style/ShapeAppearance.PDFViewer.SmallComponent</item>
    </style>

    <!-- Shape Appearances -->
    <style name="ShapeAppearance.PDFViewer.SmallComponent" parent="ShapeAppearance.Material3.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>

    <!-- Text Appearances -->
    <style name="TextAppearance.PDFViewer.Headline" parent="TextAppearance.Material3.HeadlineMedium">
    </style>

    <style name="TextAppearance.PDFViewer.Body" parent="TextAppearance.Material3.BodyLarge">
    </style>
</resources>