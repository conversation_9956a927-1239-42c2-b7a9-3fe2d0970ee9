package com.pdfviewer.app.database

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.pdfviewer.app.model.PDFCategory
import com.pdfviewer.app.model.PDFFile

@Entity(tableName = "pdf_files")
data class PDFEntity(
    @PrimaryKey
    val path: String,
    val name: String,
    val size: Long,
    val lastModified: Long,
    val pageCount: Int = 0,
    val readingProgress: Int = 0,
    val lastOpenedTime: Long = 0,
    val isFavorite: Boolean = false,
    val thumbnailPath: String? = null,
    val category: String = PDFCategory.DOCUMENT.name
) {
    fun toPDFFile(): PDFFile {
        return PDFFile(
            name = name,
            path = path,
            size = size,
            lastModified = lastModified,
            pageCount = pageCount,
            readingProgress = readingProgress,
            lastOpenedTime = lastOpenedTime,
            isFavorite = isFavorite,
            thumbnailPath = thumbnailPath,
            category = PDFCategory.valueOf(category)
        )
    }
    
    companion object {
        fun fromPDFFile(pdfFile: PDFFile): PDFEntity {
            return PDFEntity(
                path = pdfFile.path,
                name = pdfFile.name,
                size = pdfFile.size,
                lastModified = pdfFile.lastModified,
                pageCount = pdfFile.pageCount,
                readingProgress = pdfFile.readingProgress,
                lastOpenedTime = pdfFile.lastOpenedTime,
                isFavorite = pdfFile.isFavorite,
                thumbnailPath = pdfFile.thumbnailPath,
                category = pdfFile.category.name
            )
        }
    }
}

@Entity(tableName = "bookmarks")
data class BookmarkEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val pdfPath: String,
    val pageNumber: Int,
    val title: String,
    val createdTime: Long = System.currentTimeMillis(),
    val note: String? = null
)

@Entity(tableName = "reading_sessions")
data class ReadingSessionEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val pdfPath: String,
    val startTime: Long,
    val endTime: Long,
    val pagesRead: Int,
    val totalReadingTime: Long // in milliseconds
)