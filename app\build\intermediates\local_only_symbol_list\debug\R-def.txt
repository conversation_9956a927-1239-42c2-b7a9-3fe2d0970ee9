R_DEF: Internal format may change without notice
local
color black
color bookmark_color
color gradient_end
color gradient_start
color highlight_color
color info_color
color md_theme_error
color md_theme_error_container
color md_theme_on_error
color md_theme_on_error_container
color md_theme_on_primary
color md_theme_on_primary_container
color md_theme_on_secondary
color md_theme_on_secondary_container
color md_theme_on_surface
color md_theme_on_surface_variant
color md_theme_on_tertiary
color md_theme_on_tertiary_container
color md_theme_outline
color md_theme_outline_variant
color md_theme_primary
color md_theme_primary_container
color md_theme_secondary
color md_theme_secondary_container
color md_theme_surface
color md_theme_surface_variant
color md_theme_tertiary
color md_theme_tertiary_container
color overlay_dark
color overlay_light
color pdf_background
color pdf_page_shadow
color purple_200
color purple_500
color purple_700
color reading_progress
color success_color
color teal_200
color teal_700
color warning_color
color white
drawable gradient_header
drawable ic_add
drawable ic_arrow_back
drawable ic_bookmark
drawable ic_brightness_4
drawable ic_close
drawable ic_copy
drawable ic_delete
drawable ic_highlight
drawable ic_info
drawable ic_keyboard_arrow_down
drawable ic_keyboard_arrow_left
drawable ic_keyboard_arrow_right
drawable ic_keyboard_arrow_up
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_menu
drawable ic_more_vert
drawable ic_open
drawable ic_open_in_new
drawable ic_pause
drawable ic_pdf
drawable ic_pdf_document
drawable ic_play_arrow
drawable ic_search
drawable ic_settings
drawable ic_share
drawable ic_text_fields
drawable ic_view_list
drawable ic_visibility
drawable ic_visibility_off
drawable ic_zoom_in
drawable ic_zoom_out
id action_bookmark
id action_create_test_pdf
id action_delete
id action_info
id action_night_mode
id action_open
id action_open_external
id action_search
id action_settings
id action_share
id action_text_selection
id appBarLayout
id buttonAutoScroll
id buttonCopy
id buttonHighlight
id buttonMore
id buttonNextPage
id buttonNextResult
id buttonPrevPage
id buttonPrevResult
id buttonReadingMode
id buttonShare
id buttonViewMode
id buttonZoomIn
id buttonZoomOut
id cardSearchResults
id cardTextSelection
id chipAll
id chipBooks
id chipDocuments
id chipFavorites
id chipGroupFilters
id chipRecent
id collapsingToolbar
id editTextSearch
id fabAddPdf
id fabQuickActions
id imageViewPdf
id imageViewThumbnail
id layoutBottomControls
id layoutEmptyState
id layoutLoading
id progressBar
id progressLoading
id progressReading
id progressSearch
id recyclerViewPdfs
id sliderPage
id textCompleted
id textFavorites
id textInputSearch
id textLoadingStatus
id textScrollSpeed
id textSearchResults
id textSectionTitle
id textTotalPDFs
id textViewFileDate
id textViewFileName
id textViewFileSize
id textViewPageInfo
id textZoomLevel
id toolbar
id toolbarSearch
layout activity_main
layout activity_pdf_viewer
layout item_pdf
menu menu_main
menu menu_pdf_item
menu menu_pdf_viewer
mipmap ic_launcher
mipmap ic_launcher_round
string action_settings
string app_name
string first_fragment_label
string lorem_ipsum
string next
string previous
string second_fragment_label
style ShapeAppearance.PDFViewer.BottomSheet
style ShapeAppearance.PDFViewer.SmallComponent
style TextAppearance.PDFViewer.Body
style TextAppearance.PDFViewer.Headline
style Theme.PDFViewer
style Theme.PDFViewer.BottomSheetDialog
style Theme.PDFViewer.NoActionBar
style Theme.PDFViewer.SplashScreen
style Widget.PDFViewer.BottomSheet
style Widget.PDFViewer.Button
style Widget.PDFViewer.Button.Outlined
style Widget.PDFViewer.CardView
style Widget.PDFViewer.FloatingActionButton
xml backup_rules
xml data_extraction_rules
xml file_paths
