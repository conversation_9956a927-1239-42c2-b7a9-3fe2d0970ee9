{"logs": [{"outputFile": "com.pdfviewer.app-mergeDebugResources-40:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "36,37,38,39,40,41,42,68", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1902,1972,2056,2140,2236,2338,2440,5295", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1967,2051,2135,2231,2333,2435,2529,5379"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\transforms-3\\763e7e3c7daae90be5c167cae26ed6a3\\transformed\\material-1.8.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2534,2609,2720,2809,2910,3017,3124,3223,3330,3433,3521,3645,3747,3849,3965,4067,4181,4309,4425,4547,4683,4803,4937,5057,5169,5384,5501,5625,5755,5877,6015,6149,6265", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2604,2715,2804,2905,3012,3119,3218,3325,3428,3516,3640,3742,3844,3960,4062,4176,4304,4420,4542,4678,4798,4932,5052,5164,5290,5496,5620,5750,5872,6010,6144,6260,6380"}}, {"source": "D:\\apk apps\\pdf\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "35,49,48,36,41,18,20,19,21,4,6,9,11,24,26,14,16,28,29,3,5,8,10,23,25,13,15,44,45,32,33,34,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1642,2182,2132,1692,1888,847,950,897,1010,167,284,409,530,1131,1246,656,775,1315,1367,115,222,355,466,1079,1186,603,712,1982,2032,1487,1537,1590,1790,1839", "endColumns": "48,46,48,49,44,48,58,51,61,53,63,55,65,53,61,54,64,50,58,50,60,52,62,50,58,51,61,48,49,48,51,50,47,47", "endOffsets": "1686,2224,2176,1737,1928,891,1004,944,1067,216,343,460,591,1180,1303,706,835,1361,1421,161,278,403,524,1125,1240,650,769,2026,2077,1531,1584,1636,1833,1882"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,104,151,200,250,295,344,403,455,517,571,635,691,757,811,873,928,993,1044,1103,1154,1215,1268,1331,1382,1441,1493,1555,1604,1654,1703,1755,1806,1854", "endColumns": "48,46,48,49,44,48,58,51,61,53,63,55,65,53,61,54,64,50,58,50,60,52,62,50,58,51,61,48,49,48,51,50,47,47", "endOffsets": "99,146,195,245,290,339,398,450,512,566,630,686,752,806,868,923,988,1039,1098,1149,1210,1263,1326,1377,1436,1488,1550,1599,1649,1698,1750,1801,1849,1897"}}]}]}