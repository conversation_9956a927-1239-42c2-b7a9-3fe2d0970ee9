#!/usr/bin/env python3
"""
Generate PDF Viewer app icons in different sizes
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available, creating simple text-based icons")

import os

def create_pdf_icon(size, output_path):
    """Create a PDF-themed icon"""
    if PIL_AVAILABLE:
        # Create image with PIL
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Background circle
        margin = size // 10
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(98, 0, 238, 255))  # Purple background
        
        # Document shape
        doc_margin = size // 4
        doc_width = size - 2 * doc_margin
        doc_height = int(doc_width * 1.3)
        doc_top = (size - doc_height) // 2
        
        # Main document rectangle
        draw.rectangle([doc_margin, doc_top, doc_margin + doc_width, doc_top + doc_height],
                      fill=(255, 255, 255, 255), outline=(200, 200, 200, 255))
        
        # Folded corner
        corner_size = doc_width // 5
        draw.polygon([
            (doc_margin + doc_width - corner_size, doc_top),
            (doc_margin + doc_width, doc_top + corner_size),
            (doc_margin + doc_width, doc_top)
        ], fill=(224, 224, 224, 255))
        
        # PDF text (simplified)
        text_y = doc_top + doc_height // 3
        text_size = max(8, size // 12)
        
        try:
            font = ImageFont.truetype("arial.ttf", text_size)
        except:
            font = ImageFont.load_default()
            
        draw.text((doc_margin + 5, text_y), "PDF", fill=(98, 0, 238, 255), font=font)
        
        # Document lines
        line_y = text_y + text_size + 5
        for i in range(3):
            y = line_y + i * (text_size // 2)
            if y < doc_top + doc_height - 5:
                draw.line([doc_margin + 5, y, doc_margin + doc_width - 10, y], 
                         fill=(224, 224, 224, 255), width=1)
        
        img.save(output_path, 'PNG')
        print(f"Created {output_path} ({size}x{size})")
        
    else:
        # Fallback: create a simple colored square
        create_simple_icon(size, output_path)

def create_simple_icon(size, output_path):
    """Create a simple colored icon without PIL"""
    # Create a simple SVG and note that it needs conversion
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{size}" height="{size}" viewBox="0 0 {size} {size}" xmlns="http://www.w3.org/2000/svg">
    <circle cx="{size//2}" cy="{size//2}" r="{size//2-2}" fill="#6200EE"/>
    <rect x="{size//4}" y="{size//4}" width="{size//2}" height="{size//2}" fill="white" rx="2"/>
    <text x="{size//2}" y="{size//2+4}" text-anchor="middle" fill="#6200EE" font-size="{size//8}" font-family="Arial">PDF</text>
</svg>'''
    
    svg_path = output_path.replace('.png', '.svg')
    with open(svg_path, 'w') as f:
        f.write(svg_content)
    print(f"Created SVG template: {svg_path} (convert to PNG manually)")

def main():
    # Icon sizes for different densities
    sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    base_path = "app/src/main/res"
    
    for folder, size in sizes.items():
        folder_path = os.path.join(base_path, folder)
        os.makedirs(folder_path, exist_ok=True)
        
        # Create both regular and round icons
        create_pdf_icon(size, os.path.join(folder_path, 'ic_launcher.png'))
        create_pdf_icon(size, os.path.join(folder_path, 'ic_launcher_round.png'))

if __name__ == "__main__":
    main()