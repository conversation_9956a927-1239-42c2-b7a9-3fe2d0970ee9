@echo off
echo Building PDF Viewer Android App...
echo.

REM Check if Android SDK is available
if not exist "%ANDROID_HOME%" (
    echo ERROR: ANDROID_HOME environment variable is not set.
    echo Please install Android Studio and set ANDROID_HOME to your SDK path.
    echo Example: C:\Users\<USER>\AppData\Local\Android\Sdk
    pause
    exit /b 1
)

REM Clean and build the project
echo Cleaning project...
call gradlew.bat clean

echo.
echo Building debug APK...
call gradlew.bat assembleDebug

if %ERRORLEVEL% equ 0 (
    echo.
    echo ✓ Build successful!
    echo APK location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo To install on device: adb install app\build\outputs\apk\debug\app-debug.apk
) else (
    echo.
    echo ✗ Build failed!
    echo Check the error messages above.
)

pause